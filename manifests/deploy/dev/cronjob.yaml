apiVersion: batch/v1
kind: CronJob
metadata:
  namespace: mms
  name: mms-store-cronjob-custom-domain-daily-check
spec:
  #timeZone: Asia/Hong_Kong (setting invalid)
  schedule: TZ=Asia/Hong_Kong 0 3 * * *
  startingDeadlineSeconds: 300 #5min
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          activeDeadlineSeconds: 300 #5min
          containers:
            - name: mms-store-cronjob-custom-domain-daily-check-container
              image: <CI_REGISTRY>/<PROJECT_ID>/<CI_APPLICATION_REPOSITORY>:<TAGS>
              imagePullPolicy: IfNotPresent
              resources:
                requests:
                  cpu: "2"
                  memory: "1Gi"
                limits:
                  cpu: "2"
                  memory: "2Gi"
              envFrom:
                - prefix: MMS_STORE_CONFIG_
                  configMapRef:
                    name: mms-store-configmap
                - prefix: MMS_STORE_SECRET_
                  secretRef:
                    name: mms-store-secret
              command: ["java"]
              args: ["-jar", "/app.jar", "--JOB=customDomainDailyCheckJob"]
          restartPolicy: Never

---

apiVersion: batch/v1
kind: CronJob
metadata:
  namespace: mms
  name: mms-store-cronjob-custom-domain-create-check
spec:
  #timeZone: Asia/Hong_Kong (setting invalid)
  schedule: TZ=Asia/Hong_Kong * * * * *
  startingDeadlineSeconds: 300 #5min
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          activeDeadlineSeconds: 300 #5min
          containers:
            - name: mms-store-cronjob-custom-domain-create-check-container
              image: <CI_REGISTRY>/<PROJECT_ID>/<CI_APPLICATION_REPOSITORY>:<TAGS>
              imagePullPolicy: IfNotPresent
              resources:
                requests:
                  cpu: "2"
                  memory: "1Gi"
                limits:
                  cpu: "4"
                  memory: "2Gi"
              envFrom:
                - prefix: MMS_STORE_CONFIG_
                  configMapRef:
                    name: mms-store-configmap
                - prefix: MMS_STORE_SECRET_
                  secretRef:
                    name: mms-store-secret
              command: ["java"]
              args: ["-jar", "/app.jar", "--JOB=customDomainCreateCheckJob"]
          restartPolicy: Never
