package com.shoalter.mms.store.api.adapter.permission;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.StoreRepository;
import com.shoalter.mms.store.api.adapter.mms.user.dao.repository.UserMerchantRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class PermissionAdapter {

	private final UserMerchantRepository userMerchantRepository;
	private final StoreRepository storeRepository;

	public void checkRmMerchantPermission(UserDto userDto, Integer merchantId) {
		String roleCode = userDto.getRoleCode();
		int count = 0;
		switch (roleCode) {
			case "RM":
				count = userMerchantRepository.countByMerchantIdAndRmUserId(merchantId, userDto.getUserId());
				break;
			case "RML":
				count = userMerchantRepository.countByMerchantIdAndRmlUserId(merchantId, userDto.getUserId());
				break;
			case "RMO":
			case "DEPT_HEAD":
				count = userMerchantRepository.countByMerchantIdAndRmTeamUserId(merchantId, userDto.getUserId());
				break;
		}
		if(count == 0) {
			throw new AccessDeniedException("No Permission");
		}
	}

	public void checkRmStorePermission(UserDto userDto, Integer storeId) {
		String roleCode = userDto.getRoleCode();
		int count = 0;
		switch (roleCode) {
			case "RM":
				count = storeRepository.countByStoreIdAndRmUserId(storeId, userDto.getUserId());
				break;
			case "RML":
				count = storeRepository.countByStoreIdAndRmlUserId(storeId, userDto.getUserId());
				break;
			case "RMO":
			case "DEPT_HEAD":
				count = storeRepository.countByStoreIdAndRmTeamUserId(storeId, userDto.getUserId());
				break;
		}
		if(count == 0) {
			throw new AccessDeniedException("No Permission");
		}
	}

	public void checkAdminRole(UserDto userDto) {
		if(!"ADMIN".equals(userDto.getRoleCode())) {
			throw new AccessDeniedException("No Permission");
		}
	}
}
