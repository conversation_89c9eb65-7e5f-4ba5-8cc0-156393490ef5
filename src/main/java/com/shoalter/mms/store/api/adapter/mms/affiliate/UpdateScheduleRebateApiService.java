package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.AffiliateRebateSettingRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo.AffiliateRebateSettingDo;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.AffiliateRebateSettingBaseDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.ScheduleRebateSettingDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.mapper.AffiliateRebateSettingBaseDtoMapper;
import com.shoalter.mms.store.api.adapter.mms.affiliate.mapper.AffiliateRebateSettingDoMapper;
import com.shoalter.mms.store.api.adapter.permission.PermissionAdapter;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingStatusEnum;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingTypeEnum;
import com.shoalter.mms.store.api.enums.ScheduleRebateDateRangeStatus;
import com.shoalter.mms.store.api.exception.WarningException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
public class UpdateScheduleRebateApiService {
	private final AffiliateAdapter affiliateAdapter;
	private final PermissionAdapter permissionAdapter;
	private final AffiliateRebateSettingBaseDtoMapper affiliateRebateSettingBaseDtoMapper;
	private final AffiliateRebateSettingDoMapper affiliateRebateSettingDoMapper;
	private final AffiliateRebateSettingRepository affiliateRebateSettingRepository;

	public ResponseBodyDto<Void> start(UserDto userDto, Integer scheduleId, ScheduleRebateSettingDto scheduleRebateSettingDto) {
		permissionAdapter.checkAdminRole(userDto);
		AffiliateRebateSettingDo existingScheduleRebateDetailDo = affiliateRebateSettingRepository.findById(scheduleId).orElseThrow(() -> new WarningException("schedule.rebate.does.not.exist"));
		affiliateAdapter.checkStoreExist(existingScheduleRebateDetailDo.getStoreId());
		affiliateAdapter.checkDefaultRebateSettingExist(existingScheduleRebateDetailDo.getStoreId());

		ScheduleRebateDateRangeStatus rebateDateRangeStatus = checkRebateDateStatus(existingScheduleRebateDetailDo.getRebateStartDate(), existingScheduleRebateDetailDo.getRebateEndDate());
		switch (rebateDateRangeStatus) {
			case BEFORE:
				checkParameter(existingScheduleRebateDetailDo, scheduleRebateSettingDto);
				break;
			case WITHIN:
				checkParameterWithinSpecifiedUplift(existingScheduleRebateDetailDo, scheduleRebateSettingDto);
				break;
			case AFTER:
				throw new WarningException("cannot.edit.expired.schedule.rebate");
		}
		AffiliateRebateSettingDo affiliateRebateSettingDo = affiliateRebateSettingDoMapper.fromScheduleRebateSettingDto(scheduleRebateSettingDto, existingScheduleRebateDetailDo.getStoreId(), AffiliateRebateSettingStatusEnum.ACTIVE);
		affiliateRebateSettingDo.setId(existingScheduleRebateDetailDo.getId());
		affiliateRebateSettingDo.setCreatedTime(existingScheduleRebateDetailDo.getCreatedTime());
		affiliateRebateSettingRepository.save(affiliateRebateSettingDo);

		ResponseBodyDto<Void> responseBodyDto = new ResponseBodyDto<>();
		responseBodyDto.setStatus("SUCCESS");
		return responseBodyDto;
	}

	private void checkParameterWithinSpecifiedUplift(AffiliateRebateSettingDo existingRebateSettingDo, ScheduleRebateSettingDto scheduleRebateSettingDto) {
		LocalDateTime tomorrow = LocalDate.now().plusDays(1).atStartOfDay();
		LocalDateTime rebateStartDate = scheduleRebateSettingDto.getRebateStartDate().truncatedTo(ChronoUnit.HOURS);
		LocalDateTime rebateEndDate = scheduleRebateSettingDto.getRebateEndDate().truncatedTo(ChronoUnit.HOURS);
		if (!Objects.equals(rebateStartDate, existingRebateSettingDo.getRebateStartDate())) {
			throw new WarningException("start.date.cannot.be.edited.during.uplift");
		}

		if (rebateEndDate.isBefore(tomorrow)) {
			throw new WarningException("edit.end.date.must.be.greater.than.today");
		}

		if (isDateRangeOverlapping(rebateStartDate, rebateEndDate, existingRebateSettingDo)) {
			throw new WarningException("start.end.date.range.overlap");
		}

		checkValueConsistency(existingRebateSettingDo.getTierOneThreshold(), scheduleRebateSettingDto.getTierOneThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierTwoThreshold(), scheduleRebateSettingDto.getTierTwoThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierThreeThreshold(), scheduleRebateSettingDto.getTierThreeThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierFourThreshold(), scheduleRebateSettingDto.getTierFourThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierFiveThreshold(), scheduleRebateSettingDto.getTierFiveThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierSixThreshold(), scheduleRebateSettingDto.getTierSixThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierSevenThreshold(), scheduleRebateSettingDto.getTierSevenThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierEightThreshold(), scheduleRebateSettingDto.getTierEightThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierNineThreshold(), scheduleRebateSettingDto.getTierNineThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierTenThreshold(), scheduleRebateSettingDto.getTierTenThreshold());
		checkValueConsistency(existingRebateSettingDo.getTierOneRebateRate(), scheduleRebateSettingDto.getTierOneRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierTwoRebateRate(), scheduleRebateSettingDto.getTierTwoRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierThreeRebateRate(), scheduleRebateSettingDto.getTierThreeRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierFourRebateRate(), scheduleRebateSettingDto.getTierFourRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierFiveRebateRate(), scheduleRebateSettingDto.getTierFiveRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierSixRebateRate(), scheduleRebateSettingDto.getTierSixRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierSevenRebateRate(), scheduleRebateSettingDto.getTierSevenRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierEightRebateRate(), scheduleRebateSettingDto.getTierEightRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierNineRebateRate(), scheduleRebateSettingDto.getTierNineRebateRate());
		checkValueConsistency(existingRebateSettingDo.getTierTenRebateRate(), scheduleRebateSettingDto.getTierTenRebateRate());
	}

	private void checkValueConsistency(BigDecimal existTierThreshold, BigDecimal requestTierThreshold) {
		if (Objects.equals(existTierThreshold, requestTierThreshold)) {
			return;
		}
		if (existTierThreshold == null || requestTierThreshold == null || existTierThreshold.compareTo(requestTierThreshold) != 0) {
			throw new WarningException("cannot.edit.threshold.during.specified.uplift");
		}
	}

	private ScheduleRebateDateRangeStatus checkRebateDateStatus(LocalDateTime startDate, LocalDateTime endDate) {
		LocalDateTime now = LocalDateTime.now();
		if (now.isBefore(startDate)) {
			return ScheduleRebateDateRangeStatus.BEFORE;
		} else if (now.isAfter(endDate)) {
			return ScheduleRebateDateRangeStatus.AFTER;
		} else {
			return ScheduleRebateDateRangeStatus.WITHIN;
		}
	}

	private void checkParameter(AffiliateRebateSettingDo existingRebateSettingDo, ScheduleRebateSettingDto scheduleRebateSettingDto) {
		checkRequired(scheduleRebateSettingDto);

		LocalDateTime tomorrow = LocalDate.now().plusDays(1).atStartOfDay();
		LocalDateTime rebateStartDate = scheduleRebateSettingDto.getRebateStartDate().truncatedTo(ChronoUnit.HOURS);
		LocalDateTime rebateEndDate = scheduleRebateSettingDto.getRebateEndDate().truncatedTo(ChronoUnit.HOURS);
		if (rebateStartDate.isBefore(tomorrow)) {
			throw new WarningException("start.date.must.be.greater.than.today");
		}

		if (rebateStartDate.isAfter(rebateEndDate)) {
			throw new WarningException("end.date.greater.than.start.date");
		}

		if (isDateRangeOverlapping(rebateStartDate, rebateEndDate, existingRebateSettingDo)) {
			throw new WarningException("start.end.date.range.overlap");
		}

		AffiliateRebateSettingBaseDto affiliateRebateSettingBaseDto = affiliateRebateSettingBaseDtoMapper.fromScheduleRebateSettingDto(scheduleRebateSettingDto);
		affiliateAdapter.checkBaseRebateSetting(affiliateRebateSettingBaseDto);
	}

	private void checkRequired(ScheduleRebateSettingDto scheduleRebateSettingDto) {
		if (scheduleRebateSettingDto.getRebateStartDate() == null
			|| scheduleRebateSettingDto.getRebateEndDate() == null
			|| scheduleRebateSettingDto.getTierOneThreshold() == null
			|| scheduleRebateSettingDto.getTierOneRebateRate() == null) {
			throw new WarningException("schedule.rebate.required.setting.missing");
		}
	}

	private boolean isDateRangeOverlapping(LocalDateTime startDate, LocalDateTime endDate, AffiliateRebateSettingDo existingRebateSettingDo) {
		List<AffiliateRebateSettingDo> existScheduleRebateList = affiliateRebateSettingRepository.findAllByStoreIdAndRebateSettingTypeAndRebateSettingStatus(existingRebateSettingDo.getStoreId(), AffiliateRebateSettingTypeEnum.SCHEDULED, AffiliateRebateSettingStatusEnum.ACTIVE);
		return existScheduleRebateList.stream()
			.filter(affiliateRebateSettingDo -> !affiliateRebateSettingDo.getId().equals(existingRebateSettingDo.getId()))
			.anyMatch(scheduleRebate ->
				!(endDate.isBefore(scheduleRebate.getRebateStartDate()) || startDate.isAfter(scheduleRebate.getRebateEndDate())));
	}
}
