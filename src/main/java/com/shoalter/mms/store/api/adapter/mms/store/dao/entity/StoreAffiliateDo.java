package com.shoalter.mms.store.api.adapter.mms.store.dao.entity;

import com.shoalter.mms.store.api.config.converter.JsonStringConverter;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Data
@Table(name = "STORE_AFFILIATE")
public class StoreAffiliateDo {
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "ID")
	private Integer id;
	@Column(name = "STORE_ID")
	private Integer storeId;
	@Column(name = "PLATFORM_CODE")
	private String platformCode;
	@Column(name = "PLATFORM_DATA", columnDefinition = "json")
	@Convert(converter = JsonStringConverter.class)
	private String platformData;
	@Column(name = "PROGRAM_ID")
	private String programId;
	@Column(name = "HIGHEST_RATE")
	private BigDecimal highestRate;
	@Column(name = "LOWEST_RATE")
	private BigDecimal lowestRate;
	@Column(name = "SETTING_TYPE")
	private String settingType;
	@CreationTimestamp
	@Column(name = "CREATE_TIME", nullable = false, updatable = false)
	private Date createTime;
	@Column(name = "REBATE_CAP")
	private BigDecimal rebateCap;
}
