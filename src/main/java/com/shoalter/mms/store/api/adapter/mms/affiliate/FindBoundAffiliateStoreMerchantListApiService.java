package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.merchant.dao.pojo.MerchantDo;
import com.shoalter.mms.store.api.adapter.mms.merchant.dao.repository.MerchantRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class FindBoundAffiliateStoreMerchantListApiService {

    private final MerchantRepository merchantRepository;

    public ResponseBodyDto<List<MerchantDo>> start(UserDto user) {
        List<MerchantDo> merchantDoList = new ArrayList<>();
        switch (user.getRoleCode()) {
            case "ADMIN":
                merchantDoList.addAll(merchantRepository.findAllWhichStoreBoundAffiliateByMerchantId()) ;
                break;
            case "RM":
                merchantDoList.addAll(merchantRepository.findAllWhichStoreBoundAffiliateByRmUserId(user.getUserId())) ;
                break;
            case "RML":
                merchantDoList.addAll(merchantRepository.findAllWhichStoreBoundAffiliateByRmlUserId(user.getUserId())) ;
                break;
            case "RMO":
            case "DEPT_HEAD":
                merchantDoList.addAll(merchantRepository.findAllWhichStoreBoundAffiliateByDeptHeadUserId(user.getUserId())) ;
                break;
        }
        ResponseBodyDto<List<MerchantDo>> responseBodyDto = new ResponseBodyDto<>();
        responseBodyDto.setData(merchantDoList);
        responseBodyDto.setStatus("SUCCESS");
        return responseBodyDto;
    }
}
