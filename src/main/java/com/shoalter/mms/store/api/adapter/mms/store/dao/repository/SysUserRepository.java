package com.shoalter.mms.store.api.adapter.mms.store.dao.repository;

import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.SysUserEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.SysUserDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.UserStoreDo;
import com.shoalter.mms.store.api.adapter.mms.user.dao.projection.UserInfoDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

public interface SysUserRepository extends JpaRepository<SysUserEntity, Integer> {

	@Query(value = "(SELECT S.STOREFRONT_STORE_CODE AS storefrontStoreCode, " +
		"        SU.ID                   AS userId, " +
		"        SR.ROLE_CODE            AS roleCode, " +
		"        S.Active_ind            AS storeActiveInd " +
		" FROM MERCHANT_STORE MS " +
		"          JOIN STORE S ON S.Id = MS.Store_id AND S.STOREFRONT_STORE_CODE in :storefrontStoreCodes " +
		"          JOIN BUS_UNIT BU ON S.BUS_UNIT_ID = BU.ID AND BU.CODE in :buCodes " +
		"          JOIN MERCHANT M ON MS.MERCHANT_ID = M.ID " +
		"          JOIN SYS_USER SU ON SU.MERCHANT_ID = M.ID and SU.ACTIVE_IND = 'Y' " +
		"          JOIN SYS_USER_ROLE SUR on SU.ID = SUR.USER_ID " +
		"          JOIN SYS_ROLE SR on SR.ID = SUR.ROLE_ID " +
		"          LEFT JOIN USER_STORE US ON US.USER_ID = SU.ID " +
		" WHERE (US.USER_ID IS NULL OR US.STORE_ID = S.Id) " +
		" ORDER BY SU.CREATED_DATE) " +
		"UNION " +
		"(SELECT s.STOREFRONT_STORE_CODE AS storefrontStoreCode, " +
		"        su.ID                   AS userId, " +
		"        sr.ROLE_CODE            AS roleCode, " +
		"        s.Active_ind            AS storeActiveInd " +
		" FROM MERCHANT_STORE ms " +
		"          JOIN STORE s ON ms.Store_id = s.Id AND s.STOREFRONT_STORE_CODE in :storefrontStoreCodes " +
		"          JOIN BUS_UNIT BU ON s.BUS_UNIT_ID = BU.ID AND BU.CODE in :buCodes " +
		"          JOIN USER_MERCHANT um on um.MERCHANT_ID = ms.MERCHANT_id " +
		"          JOIN SYS_USER su ON um.USER_ID = su.ID and su.ACTIVE_IND = 'Y' " +
		"          JOIN SYS_USER_ROLE sur ON sur.USER_ID = su.ID " +
		"          JOIN SYS_ROLE sr ON sr.ID = sur.ROLE_ID " +
		"          LEFT JOIN RM_TEAM rt ON um.USER_ID = rt.USER_ID " +
		" WHERE sr.ROLE_CODE in ('RML', 'RMO', 'DEPT_HEAD', 'RM', 'SRM') " +
		"   and CASE sr.ROLE_CODE " +
		"           WHEN 'RML' THEN rt.TEAM_LEADER_ID = su.ID " +
		"           WHEN 'RMO' THEN rt.DEPT_CODE IN " +
		"                           (SELECT RMO.DEPT_CODE FROM RM_TEAM RMO WHERE RMO.USER_ID = su.ID) " +
		"           WHEN 'DEPT_HEAD' THEN rt.DEPT_HEAD_ID = su.ID " +
		"           ELSE TRUE " +
		"     END)" +
		"UNION " +
		"(SELECT S.STOREFRONT_STORE_CODE AS storefrontStoreCode, " +
		"        SU.ID                   AS userId, " +
		"        SR.ROLE_CODE            AS roleCode, " +
		"        S.Active_ind            AS storeActiveInd " +
		" FROM SYS_USER SU " +
		"          JOIN SYS_USER_ROLE sur ON sur.USER_ID = SU.ID " +
		"          JOIN SYS_ROLE SR ON SR.ID = sur.ROLE_ID " +
		"          JOIN STORE S " +
		"          JOIN BUS_UNIT BU ON S.BUS_UNIT_ID = BU.ID AND BU.CODE in :buCodes " +
		" WHERE S.STOREFRONT_STORE_CODE in :storefrontStoreCodes " +
		"   AND SR.ROLE_TYPE = 'S' " +
		"   AND SU.ACTIVE_IND = 'Y' " +
		"   AND SR.ROLE_CODE not in ('RML', 'RMO', 'DEPT_HEAD', 'RM', 'SRM', 'MERCHANT_ADMIN', 'MERCHANT'))", nativeQuery = true)
	List<UserStoreDo> findUserStoreInfoByStorefrontStoreCodes(List<String> storefrontStoreCodes, Set<String> buCodes);

	@Query(value =
		"SELECT SU.ID AS userId, " +
			"SU.EMAIL AS email, " +
			"SU.USER_NAME AS userName " +
			"	FROM SYS_USER SU " +
			"	JOIN USER_BUS_UNIT UBU on SU.ID = UBU.USER_ID " +
			"WHERE SU.MERCHANT_ID = :merchantId " +
			"AND UBU.BUS_UNIT_ID = 107"
		, nativeQuery = true)
	List<SysUserDo> findUserIdForLMByMerchantId(Integer merchantId);

	@Query(value =
		"select su.ID          as userId, " +
			"       su.USER_NAME   as userName, " +
			"       su.USER_CODE   as userCode, " +
			"       su.MERCHANT_ID as merchantId, " +
			"       su.EMAIL       as email, " +
			"       sr.ID          as roleId, " +
			"       sr.ROLE_CODE   as roleCode, " +
			"       sr.ROLE_TYPE   as roleType, " +
			"       group_concat(bu.CODE) as buCodes " +
			"from SYS_USER su " +
			"         join SYS_USER_ROLE sur on sur.USER_ID = su.ID " +
			"         join SYS_ROLE sr on sr.ID = sur.ROLE_ID " +
			"         join USER_BUS_UNIT ubu on ubu.USER_ID = su.ID " +
			"         join BUS_UNIT bu on bu.ID = ubu.BUS_UNIT_ID and bu.ACTIVE_IND = 'Y' " +
			"where su.ID in :userIds " +
			"group by su.ID"
		, nativeQuery = true)
	List<UserInfoDo> findUserInfoByUserIds(List<Integer> userIds);

}
