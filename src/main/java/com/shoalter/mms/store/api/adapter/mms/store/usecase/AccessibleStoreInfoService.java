package com.shoalter.mms.store.api.adapter.mms.store.usecase;

import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.ContractEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.RmTeamEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.StoreEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.ContractRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.RmTeamRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dto.AccessibleStoreInfoResponseDto;
import com.shoalter.mms.store.api.enums.RoleCodeEnum;
import com.shoalter.mms.store.api.enums.RoleType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.IntFunction;
import java.util.stream.Collectors;

/**
 * Service responsible for managing and retrieving accessible store information based on user roles.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AccessibleStoreInfoService {

	private final ContractRepository contractRepository;
	private final RmTeamRepository rmTeamRepository;

	// Supported role set for accessible store information retrieval
	private static final Set<String> ROLE_SET = Set.of(
		RoleCodeEnum.RM.name(),
		RoleCodeEnum.SRM.name(),
		RoleCodeEnum.RML.name(),
		RoleCodeEnum.RMO.name(),
		RoleCodeEnum.DEPT_HEAD.name()
	);

	// Strategy map for role-based RM ID collection
	private final Map<RoleCodeEnum, IntFunction<Set<Integer>>> rmIdCollectionStrategies = Map.of(
		RoleCodeEnum.RM, this::collectRmIds,
		RoleCodeEnum.SRM, this::collectSRmIds,
		RoleCodeEnum.RML, this::collectRmlIds,
		RoleCodeEnum.RMO, this::collectRmoIds,
		RoleCodeEnum.DEPT_HEAD, this::collectDeptHeadIds
	);

	/**
	 * Retrieves accessible stores based on user ID and role.
	 */
	public List<AccessibleStoreInfoResponseDto> getAccessibleStores(Integer userId, RoleCodeEnum role) {
		Assert.notNull(userId, "[getAccessibleStores] User ID cannot be null");
		Assert.notNull(role, "[getAccessibleStores] Role cannot be null");

		IntFunction<Set<Integer>> strategy = rmIdCollectionStrategies.get(role);
		if (strategy == null) {
			log.warn("[getAccessibleStores] Unsupported role: {} for userId: {}", role, userId);
			return Collections.emptyList();
		}

		Set<Integer> rmIds = strategy.apply(userId);
		if (rmIds.isEmpty()) {
			log.info("[getAccessibleStores] No accessible stores found for userId: {}, role: {}", userId, role);
			return Collections.emptyList();
		}

		List<ContractEntity> contracts = contractRepository.findByRmIdsWithStore(rmIds);
		return contracts.stream()
			.map(this::mapToStoreInfoResponse)
			.collect(Collectors.toList());
	}

	private Set<Integer> collectRmIds(Integer userId) {
		return Collections.singleton(userId);
	}

	private Set<Integer> collectSRmIds(Integer userId) {
		Set<Integer> rmIds = new HashSet<>();
		rmIds.add(userId);

		List<RmTeamEntity> sRmTeamMembers = rmTeamRepository.findBySrmId(userId);
		rmIds.addAll(sRmTeamMembers.stream()
			.map(RmTeamEntity::getUserId)
			.collect(Collectors.toSet()));

		return rmIds;
	}

	private Set<Integer> collectRmlIds(Integer userId) {
		Set<Integer> rmIds = new HashSet<>();
		rmIds.add(userId);

		List<RmTeamEntity> rmlTeamMembers = rmTeamRepository.findByTeamLeaderId(userId);
		rmIds.addAll(rmlTeamMembers.stream()
			.map(RmTeamEntity::getUserId)
			.collect(Collectors.toSet()));
		return rmIds;
	}

	private Set<Integer> collectRmoIds(Integer userId) {
		Set<Integer> rmIds = new HashSet<>();
		rmIds.add(userId);

		List<RmTeamEntity> deptMembers = rmTeamRepository.findRmsByRmoDeptCode(userId);
		rmIds.addAll(deptMembers.stream()
			.map(RmTeamEntity::getUserId)
			.collect(Collectors.toSet()));
		return rmIds;
	}

	private Set<Integer> collectDeptHeadIds(Integer userId) {
		List<RmTeamEntity> deptRms = rmTeamRepository.findByDeptHeadId(userId);
		return deptRms.stream()
			.map(RmTeamEntity::getUserId)
			.collect(Collectors.toSet());
	}

	private AccessibleStoreInfoResponseDto mapToStoreInfoResponse(ContractEntity contract) {
		AccessibleStoreInfoResponseDto dto = new AccessibleStoreInfoResponseDto();
		dto.setStoreId(contract.getStoreId());
		dto.setStoreFrontStoreCode(Optional.ofNullable(contract.getStore())
			.map(StoreEntity::getStorefrontStoreCode)
			.orElse(null));
		dto.setUserId(contract.getRmId());
		dto.setContractId(contract.getId());
		dto.setContractNo(contract.getContractNo());
		dto.setContractStartDate(contract.getStartDate());
		dto.setContractEndDate(contract.getEndDate());
		dto.setContractStatus(contract.getStatus());
		return dto;
	}

	/**
	 * Determines if accessible store information check is needed based on role type and code.
	 *
	 * @param roleType The type of role
	 * @param roleCode The role code
	 * @return true if check is needed, false otherwise
	 */
	public static boolean needCheckAccessibleStoreInfo(RoleType roleType, String roleCode) {
		if (isMerchant(roleType)) {
			return false;
		}

		if (isSystemUser(roleType) && roleCode != null) {
			return ROLE_SET.contains(roleCode);
		}

		return false;
	}

	private static boolean isSystemUser(RoleType roleType) {
		return RoleType.S == roleType;
	}

	private static boolean isMerchant(RoleType roleType) {
		return RoleType.M == roleType;
	}
}
