package com.shoalter.mms.store.api.adapter.mms.store.dto;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.Set;

@Data
@Builder
public class FindStoreBuRequest {
	Set<String> buCodes;
	Integer merchantId;
	Boolean displayInactiveStore;
	Boolean isIncludingVirtualStore;
	String source;
	Set<String> contractStatus;
	RequestType requestType;

	@Getter
	public enum RequestType {

		/**
		 * New flow, call from /api/stores/storeInfo
		 * Support filter results by RM accessible stores, ROLE_CODE in (RM, RML, RMO, HEAD_DEPT)
		 */
		NEW_FLOW("new flow, call from /api/stores/storeInfo");

		private final String message;

		RequestType(String message) {
			this.message = message;
		}
	}
}
