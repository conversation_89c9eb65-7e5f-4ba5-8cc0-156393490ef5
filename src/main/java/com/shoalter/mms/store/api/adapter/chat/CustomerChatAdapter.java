package com.shoalter.mms.store.api.adapter.chat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms.store.api.adapter.chat.dto.CustomerChatResponseDto;
import com.shoalter.mms.store.api.adapter.chat.dto.UpdateCustomerChatStoreRequestDto;
import com.shoalter.mms.store.api.adapter.chat.dto.UpdateCustomerChatUserByStoreRequestDto;
import com.shoalter.mms.store.api.adapter.chat.dto.UpdateCustomerChatUserByUserRequestDto;
import com.shoalter.mms.store.api.adapter.http.HttpGateway;
import com.shoalter.mms.store.api.adapter.http.dto.HttpRequestDto;
import com.shoalter.mms.store.api.adapter.http.exception.HttpException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.HttpStatusCodeException;

import java.util.List;
import java.util.Optional;

@Slf4j
@Validated
@RequiredArgsConstructor
@Component
public class CustomerChatAdapter {
	private final HttpGateway httpGateway;
	private final ObjectMapper objectMapper;

	@Value("${customer-chat.base-url}")
	private String customerChatBaseUrl;

	private final String customerChatUpdateStoreEndpoint = "/mmschatapi/api/internal/merchant/stores";
	private final String customerChatUpdateUserByStoreEndpoint = "/mmschatapi/api/internal/merchant/stores/users";
	private final String customerChatUpdateUserByUserEndpoint = "/mmschatapi/api/internal/merchant/users/stores";


	public Optional<CustomerChatResponseDto> updateStore(List<UpdateCustomerChatStoreRequestDto> requestDto) {
		var request =
			HttpRequestDto.<List<UpdateCustomerChatStoreRequestDto>, CustomerChatResponseDto>builder()
				.url(customerChatBaseUrl + customerChatUpdateStoreEndpoint)
				.method(HttpMethod.POST)
				.headers(getHeaders())
				.body(requestDto)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();

		return exchangeForBody(request);
	}

	public Optional<CustomerChatResponseDto> updateUserByStore(List<UpdateCustomerChatUserByStoreRequestDto> requestDto) {
		var request =
			HttpRequestDto.<List<UpdateCustomerChatUserByStoreRequestDto>, CustomerChatResponseDto>builder()
				.url(customerChatBaseUrl + customerChatUpdateUserByStoreEndpoint)
				.method(HttpMethod.POST)
				.headers(getHeaders())
				.body(requestDto)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<CustomerChatResponseDto> updateUserByUser(List<UpdateCustomerChatUserByUserRequestDto> requestDto) {
		var request =
			HttpRequestDto.<List<UpdateCustomerChatUserByUserRequestDto>, CustomerChatResponseDto>builder()
				.url(customerChatBaseUrl + customerChatUpdateUserByUserEndpoint)
				.method(HttpMethod.POST)
				.headers(getHeaders())
				.body(requestDto)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}


	public HttpHeaders getHeaders() {
		var headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		return headers;
	}

	private <T> Optional<CustomerChatResponseDto> exchangeForBody(HttpRequestDto<T, CustomerChatResponseDto> request) {
		try {
			return httpGateway.exchangeForBody(request);
		} catch (HttpException e) {
			if (e.getCause() instanceof HttpStatusCodeException) {
				HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) e.getCause();
				if (httpStatusCodeException.getStatusCode().is4xxClientError()) {
					try {
						CustomerChatResponseDto response = objectMapper.readValue(httpStatusCodeException.getResponseBodyAsString(), new TypeReference<>() {
						});
						log.error(e.getMessage(), e);
						return Optional.ofNullable(response);
					} catch (JsonProcessingException ex) {
						throw e;
					}
				}
			}
			throw e;
		}
	}

}
