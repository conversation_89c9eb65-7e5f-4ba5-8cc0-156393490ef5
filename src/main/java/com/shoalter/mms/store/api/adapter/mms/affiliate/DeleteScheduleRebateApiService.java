package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.AffiliateRebateSettingRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo.AffiliateRebateSettingDo;
import com.shoalter.mms.store.api.adapter.permission.PermissionAdapter;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingStatusEnum;
import com.shoalter.mms.store.api.exception.WarningException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class DeleteScheduleRebateApiService {

	private final PermissionAdapter permissionAdapter;
	private final AffiliateRebateSettingRepository affiliateRebateSettingRepository;

	public ResponseBodyDto<Void> start(UserDto userDto, Integer scheduleId) {
		permissionAdapter.checkAdminRole(userDto);
		start(scheduleId);
		ResponseBodyDto<Void> responseBodyDto = new ResponseBodyDto<>();
		responseBodyDto.setStatus("SUCCESS");
		return responseBodyDto;
	}

	public void start(Integer scheduleId) {
		AffiliateRebateSettingDo rebateSettingDo = affiliateRebateSettingRepository.findById(scheduleId).orElse(null);
		if(rebateSettingDo != null) {
			if (isInScheduleRebateRangeDate(rebateSettingDo)) {
				throw new WarningException("cannot.delete.schedule.rebate.setting");
			}
			rebateSettingDo.setRebateSettingStatus(AffiliateRebateSettingStatusEnum.INACTIVE);
			affiliateRebateSettingRepository.save(rebateSettingDo);
		}
	}

	private boolean isInScheduleRebateRangeDate(AffiliateRebateSettingDo rebateSettingDo) {
		LocalDateTime now = LocalDateTime.now();
		return now.isAfter(rebateSettingDo.getRebateStartDate()) && now.isBefore(rebateSettingDo.getRebateEndDate());
	}
}
