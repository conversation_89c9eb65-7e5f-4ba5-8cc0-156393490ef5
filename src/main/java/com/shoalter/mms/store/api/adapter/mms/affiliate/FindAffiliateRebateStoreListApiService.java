package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.AffiliateRebateSettingRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo.AffiliateRebateStoreDo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class FindAffiliateRebateStoreListApiService {

	private final AffiliateRebateSettingRepository affiliateRebateSettingRepository;

	public ResponseBodyDto<List<AffiliateRebateStoreDo>> start(UserDto user) {
		List<AffiliateRebateStoreDo> storeList = new ArrayList<>();
		switch (user.getRoleCode()) {
			case "ADMIN":
				storeList.addAll(affiliateRebateSettingRepository.findAllByDefaultSettingType());
				break;
			case "RM":
				storeList.addAll(affiliateRebateSettingRepository.findAllByDefaultSettingTypeAndRmUserId(user.getUserId())) ;
				break;
			case "RML":
				storeList.addAll(affiliateRebateSettingRepository.findAllByDefaultSettingTypeAndRmlUserId(user.getUserId())) ;
				break;
			case "RMO":
			case "DEPT_HEAD":
				storeList.addAll(affiliateRebateSettingRepository.findAllByDefaultSettingTypeAndDeptHeadUserId(user.getUserId())) ;
				break;
		}
		ResponseBodyDto<List<AffiliateRebateStoreDo>> responseBodyDto = new ResponseBodyDto<>();
		responseBodyDto.setData(storeList);
		responseBodyDto.setStatus("SUCCESS");
		return responseBodyDto;
	}
}
