package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.StoreAffiliateRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo.BoundAffiliateStoreDo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class FindStoreAffiliateBoundStoreListApiService {

    private final StoreAffiliateRepository storeAffiliateRepository;

    public ResponseBodyDto<List<BoundAffiliateStoreDo>> start(UserDto user) {
        List<BoundAffiliateStoreDo> storeList = new ArrayList<>();
        switch (user.getRoleCode()) {
            case "ADMIN":
                storeList.addAll(storeAffiliateRepository.findAllByBoundAffiliate());
                break;
            case "RM":
                storeList.addAll(storeAffiliateRepository.findAllByBoundAffiliateStoreAndRmUserId(user.getUserId())) ;
                break;
            case "RML":
                storeList.addAll(storeAffiliateRepository.findAllByBoundAffiliateAndRmlUserId(user.getUserId())) ;
                break;
            case "RMO":
            case "DEPT_HEAD":
                storeList.addAll(storeAffiliateRepository.findAllByBoundAffiliateAndDeptHeadUserId(user.getUserId())) ;
                break;
        }
        ResponseBodyDto<List<BoundAffiliateStoreDo>> responseBodyDto = new ResponseBodyDto<>();
        responseBodyDto.setData(storeList);
        responseBodyDto.setStatus("SUCCESS");
        return responseBodyDto;
    }
}
