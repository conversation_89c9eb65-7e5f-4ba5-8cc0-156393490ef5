package com.shoalter.mms.store.api.adapter.mms.referral_link.usecase;

import com.shoalter.mms.store.api.adapter.controller.dto.ResponseDto;
import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.kutt.KuttApiAdapter;
import com.shoalter.mms.store.api.adapter.kutt.dto.ReferralLinkDetailResponseDto;
import com.shoalter.mms.store.api.adapter.kutt.dto.ReferralLinkPageResponseDto;
import com.shoalter.mms.store.api.adapter.kutt.dto.ShortLinkRequestDto;
import com.shoalter.mms.store.api.adapter.mms.helper.CacheHelper;
import com.shoalter.mms.store.api.adapter.mms.product.dto.PageResult;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkCreateRequest;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkExportRequest;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkOptionResponse;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkPageResponse;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindStoreBuRequest;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindStoresResponse;
import com.shoalter.mms.store.api.adapter.mms.store.usecase.FindStoresUseCase;
import com.shoalter.mms.store.api.enums.BuCodeEnum;
import com.shoalter.mms.store.api.enums.ReferralLinkExcelColumnEnum;
import com.shoalter.mms.store.api.enums.SystemParamEnum;
import com.shoalter.mms.store.api.exception.WarningException;
import com.shoalter.mms.store.api.util.DateTimeUtil;
import com.shoalter.mms.store.api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shoalter.mms.store.api.util.StringUtil.UNDERSTORE;

@RequiredArgsConstructor
@Service
@Slf4j
public class ReferralLinkUseCase {
	private final MessageSource messageSource;
	private final KuttApiAdapter kuttApiAdapter;
	private final FindStoresUseCase findStoresUseCase;
	private final CacheHelper cacheHelper;

	private static final String SOURCE_PARAM = "utm_source";
	private static final String MEDIUM_PARAM = "utm_medium";
	private static final String CAMPAIGN_PARAM = "utm_campaign";
	private static final String CAMPAIGN_HKTV_ATTR = "attr_";
	private static final String CAMPAIGN_THE_PLACE_ATTR = "theplaceAttr_";
	private static final int VALID_YEAR_SINCE = 2000;

	public ResponseDto<List<ReferralLinkOptionResponse>> getSource() {
		return ResponseDto.success(getReferralLinkOptions(SystemParamEnum.REFERRAL_LINK_SOURCE));
	}

	public ResponseDto<List<ReferralLinkOptionResponse>> getMedium() {
		return ResponseDto.success(getReferralLinkOptions(SystemParamEnum.REFERRAL_LINK_MEDIUM));
	}

	public ResponseDto<PageResult<ReferralLinkPageResponse>> getReferralLinkPage(UserDto user, int pageSize, int pageNumber, Set<String> storefrontStoreCodes) {
		//check
		List<FindStoresResponse> stores = checkStorePermission(user, storefrontStoreCodes);
		BuCodeEnum buCodeEnum = BuCodeEnum.HKTV.name().equals(stores.get(0).getBusUnitCode()) ? BuCodeEnum.HKTV : BuCodeEnum.LITTLE_MALL;

		//call api
		ReferralLinkPageResponseDto result = kuttApiAdapter.getShortLink(buCodeEnum, (pageNumber - 1) * pageSize, pageSize, storefrontStoreCodes).orElseThrow();
		if (result.getError() != null) {
			return ResponseDto.fail(messageSource.getMessage("message23", new String[]{result.getError()}, null));
		}

		//response
		PageResult<ReferralLinkPageResponse> pageResult = PageResult.<ReferralLinkPageResponse>builder()
			.pageNumber(pageNumber)
			.pageSize(pageSize)
			.totalItems(result.getTotal())
			.totalPages((int) Math.ceil((double) result.getTotal() / pageSize))
			.list(result.getData().stream().map(this::generateReferralLinkPageResponse).collect(Collectors.toList()))
			.build();

		return ResponseDto.success(pageResult);
	}

	public ResponseDto<ReferralLinkPageResponse> createReferralLink(UserDto user, ReferralLinkCreateRequest request) {
		//check
		List<FindStoresResponse> stores = checkStorePermission(user, Set.of(request.getStorefrontStoreCode()));
		BuCodeEnum buCodeEnum = checkCreateRequest(stores.get(0), request);

		//call api
		ReferralLinkDetailResponseDto result = kuttApiAdapter.createShortLink(buCodeEnum,
			new ShortLinkRequestDto(generateUrl(buCodeEnum, request), request.getStorefrontStoreCode())).orElseThrow();
		if (result.getError() != null) {
			return ResponseDto.fail(messageSource.getMessage("message23", new String[]{result.getError()}, null));
		}

		return ResponseDto.success(generateReferralLinkPageResponse(result));
	}

	public HttpEntity<ByteArrayResource> exportReferralLinks(UserDto user, ReferralLinkExportRequest request) throws IOException {
		//check
		List<FindStoresResponse> store = checkStorePermission(user, request.getStorefrontStoreCodes());
		BuCodeEnum buCodeEnum = BuCodeEnum.HKTV.name().equals(store.get(0).getBusUnitCode()) ? BuCodeEnum.HKTV : BuCodeEnum.LITTLE_MALL;

		//call api
		ReferralLinkPageResponseDto result = kuttApiAdapter.getShortLink(buCodeEnum, null, null, request.getStorefrontStoreCodes()).orElseThrow();
		if (result.getError() != null) {
			throw new WarningException("message23", result.getError());
		}

		if (result.getTotal() == 0) {
			throw new WarningException("message26");
		}

		//generate excel
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		try (Workbook workbook = new XSSFWorkbook()) {
			Sheet dataSheet = workbook.createSheet();
			setExcelHeader(dataSheet);
			setExcelBody(workbook, dataSheet, result);
			workbook.write(outputStream);
		} catch (IOException e) {
			log.error("export error : {}, ", e.getMessage(), e);
			throw e;
		}
		return new ResponseEntity<>(new ByteArrayResource(outputStream.toByteArray()), getResponseHeader(), HttpStatus.OK);
	}

	private void setExcelHeader(Sheet dataSheet) {
		Row row = dataSheet.createRow(0);
		row.setHeight((short) (30 * 20));
		int colNum = 0;
		for (ReferralLinkExcelColumnEnum columnEnum : ReferralLinkExcelColumnEnum.values()) {
			Cell cell = CellUtil.getCell(row, colNum);
			cell.setCellValue(columnEnum.getColName());
			colNum++;
		}
	}

	private void setExcelBody(Workbook workbook, Sheet dataSheet, ReferralLinkPageResponseDto result) {
		CreationHelper creationHelper = workbook.getCreationHelper();
		CellStyle dateCellStyle = workbook.createCellStyle();
		dateCellStyle.setDataFormat(creationHelper.createDataFormat().getFormat(DateTimeUtil.DATE_TIME));
		Row row;
		for (ReferralLinkDetailResponseDto item : result.getData()) {
			Pair<String, Map<String, String>> pair = getUrlInfo(item.getTarget());
			Map<String, String> paramMap = pair.getValue();
			row = dataSheet.createRow(dataSheet.getLastRowNum() + 1);
			row.createCell(ReferralLinkExcelColumnEnum.STORE_CODE.getColumnNumber()).setCellValue(item.getDescription());
			row.createCell(ReferralLinkExcelColumnEnum.SHORTEN_LINK.getColumnNumber()).setCellValue(item.getLink());
			row.createCell(ReferralLinkExcelColumnEnum.SOURCE.getColumnNumber()).setCellValue(paramMap.get(SOURCE_PARAM));
			row.createCell(ReferralLinkExcelColumnEnum.MEDIUM.getColumnNumber()).setCellValue(paramMap.get(MEDIUM_PARAM));
			row.createCell(ReferralLinkExcelColumnEnum.CAMPAIGN.getColumnNumber()).setCellValue(paramMap.get(CAMPAIGN_PARAM));
			Cell dateCell = row.createCell(ReferralLinkExcelColumnEnum.UPDATE_TIME.getColumnNumber());
			dateCell.setCellValue(item.getCreatedAt());
			dateCell.setCellStyle(dateCellStyle);
		}
	}

	private List<FindStoresResponse> checkStorePermission(UserDto user, Collection<String> storefrontStoreCodes) {
		if (storefrontStoreCodes == null || storefrontStoreCodes.isEmpty()) {
			throw new WarningException("message6");
		}

		if (storefrontStoreCodes.size() > 20) {
			throw new WarningException("message27");
		}

		FindStoreBuRequest findStoreBuRequest = FindStoreBuRequest.builder()
			.displayInactiveStore(true)
			.buCodes(BuCodeEnum.DEFAULT_STORES_QUERY_CODES)
			.build();

		Map<String, FindStoresResponse> permittedStores = new HashMap<>();
		findStoresUseCase.getStores(user, findStoreBuRequest).forEach(store -> permittedStores.put(store.getStoreFrontStoreCode(), store));
		List<String> notAllowStores = new ArrayList<>();
		List<FindStoresResponse> result = new ArrayList<>();
		storefrontStoreCodes.forEach(storeCode -> {
			if (permittedStores.containsKey(storeCode)) {
				result.add(permittedStores.get(storeCode));
			} else {
				notAllowStores.add(storeCode);
			}
		});

		if (!notAllowStores.isEmpty()) {
			throw new WarningException("message24", notAllowStores.toString());
		}

		return result;
	}

	private BuCodeEnum checkCreateRequest(FindStoresResponse store, ReferralLinkCreateRequest request) {
		//check page url
		BuCodeEnum buCodeEnum = checkPageUrl(store, request.getPageUrl());

		//check source
		if (request.getSources() != null) {
			request.getSources().forEach(this::checkOnlyAlphabetAndUnderScore);
		}

		//check medium
		if (request.getMediums() != null) {
			request.getMediums().forEach(this::checkOnlyAlphabetAndUnderScore);
		}

		//check campaign description
		if (StringUtil.isNotNullOrBlank(request.getCampaignDescription())) {
			checkOnlyAlphabetAndUnderScore(request.getCampaignDescription());
		}

		//check campaign date
		if (StringUtil.isNotNullOrBlank(request.getCampaignDate())) {
			try {
				LocalDate date = LocalDate.parse(request.getCampaignDate(), DateTimeFormatter.ofPattern(DateTimeUtil.DATE_WITH_ONLY_LETTER));
				if (date.getYear() < VALID_YEAR_SINCE) {
					throw new WarningException("message22");
				}
			} catch (DateTimeParseException e) {
				throw new WarningException("message21");
			}
		}

		return buCodeEnum;
	}

	private BuCodeEnum checkPageUrl(FindStoresResponse store, String pageUrl) {
		Map<BuCodeEnum, Set<String>> buCodeMap = new HashMap<>();
		cacheHelper.findSystemParamBySegment(SystemParamEnum.REFERRAL_LINK_DOMAIN)
			.forEach(systemParamEntity -> {
				if (systemParamEntity.getCode().equals(BuCodeEnum.HKTV.name())) {
					buCodeMap.put(BuCodeEnum.HKTV, Set.of(systemParamEntity.getParmValue().split(StringUtil.COMMA)));
				} else {
					buCodeMap.put(BuCodeEnum.LITTLE_MALL, Set.of(systemParamEntity.getParmValue().split(StringUtil.COMMA)));
				}
			});

		BuCodeEnum buCodeEnum;

		if (StringUtil.isNullOrBlank(pageUrl)) {
			throw new WarningException("message25");
		}

		if (pageUrl.matches(StringUtil.CONTAIN_SPACE)) {
			throw new WarningException("message19");
		}

		if (BuCodeEnum.HKTV.name().equals(store.getBusUnitCode())) {
			buCodeEnum = BuCodeEnum.HKTV;
			if (buCodeMap.get(buCodeEnum).stream().noneMatch(pageUrl::startsWith)) {
				throw new WarningException("message17");
			}
		} else {
			buCodeEnum = BuCodeEnum.LITTLE_MALL;
			if (buCodeMap.get(buCodeEnum).stream().noneMatch(pageUrl::startsWith)) {
				throw new WarningException("message18");
			}
		}
		return buCodeEnum;
	}

	private void checkOnlyAlphabetAndUnderScore(String value) {
		if (!value.matches(StringUtil.ALPHABET_AND_NUMBER_AND_UNDERSCORE)) {
			throw new WarningException("message20");
		}
	}

	protected String generateUrl(BuCodeEnum buCodeEnum, ReferralLinkCreateRequest request) {
		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(request.getPageUrl());
		if (request.getSources() != null && !request.getSources().isEmpty()) {
			builder.queryParam(SOURCE_PARAM, String.join(StringUtil.PLUS, request.getSources()));
		}
		if (request.getMediums() != null && !request.getMediums().isEmpty()) {
			builder.queryParam(MEDIUM_PARAM, String.join(StringUtil.PLUS, request.getMediums()));
		}
		String attr = buCodeEnum == BuCodeEnum.HKTV ? CAMPAIGN_HKTV_ATTR : CAMPAIGN_THE_PLACE_ATTR;
		StringBuilder campaign = new StringBuilder(attr).append(request.getStorefrontStoreCode());

		// 確保 utm_campaign 參數在所有情況下都會出現
		if (StringUtil.isNotNullOrBlank(request.getCampaignDescription()) && StringUtil.isNotNullOrBlank(request.getCampaignDate())) {
			// 兩者都有填
			builder.queryParam(CAMPAIGN_PARAM, campaign.append(StringUtil.UNDERSTORE)
				.append(request.getCampaignDescription())
				.append(StringUtil.UNDERSTORE)
				.append(request.getCampaignDate()));
		} else if (StringUtil.isNotNullOrBlank(request.getCampaignDescription())) {
			// 只有 campaignDescription 有填
			builder.queryParam(CAMPAIGN_PARAM, campaign.append(StringUtil.UNDERSTORE)
				.append(request.getCampaignDescription()));
		} else if (StringUtil.isNotNullOrBlank(request.getCampaignDate())) {
			// 只有 campaignDate 有填
			builder.queryParam(CAMPAIGN_PARAM, campaign.append(StringUtil.UNDERSTORE)
				.append(request.getCampaignDate()));
		} else {
			// 兩者都沒填，仍然添加基本的 utm_campaign 參數
			builder.queryParam(CAMPAIGN_PARAM, campaign.toString());
		}

		builder.queryParam("openinapp", true);
		builder.queryParam("autoTriggerApp", true);
		builder.queryParam("fastrender", true);
		builder.queryParam("backstack", true);

		return builder.build().toUriString();
	}


	private List<ReferralLinkOptionResponse> getReferralLinkOptions(SystemParamEnum systemParamEnum) {
		return cacheHelper.findSystemParamBySegment(systemParamEnum).stream()
			.map(sysParam -> ReferralLinkOptionResponse.builder()
				.name(sysParam.getShortDescTc() == null ? sysParam.getShortDesc() : sysParam.getShortDesc() + StringUtils.SPACE + sysParam.getShortDescTc())
				.value(sysParam.getParmValue())
				.build()).collect(Collectors.toList());
	}


	private ReferralLinkPageResponse generateReferralLinkPageResponse(ReferralLinkDetailResponseDto referralLinkDetailResponseDto) {
		ReferralLinkPageResponse response = ReferralLinkPageResponse.builder()
			.storeCode(referralLinkDetailResponseDto.getDescription())
			.link(referralLinkDetailResponseDto.getLink())
			.createdTime(referralLinkDetailResponseDto.getCreatedAt().getTime())
			.build();
		Pair<String, Map<String, String>> pair = getUrlInfo(referralLinkDetailResponseDto.getTarget());
		Map<String, String> queryPairs = pair.getRight();
		response.setPageUrl(pair.getLeft());
		if (queryPairs.isEmpty()) {
			return response;
		}

		if (queryPairs.containsKey(SOURCE_PARAM)) {
			Map<String, ReferralLinkOptionResponse> sourceMap = getReferralLinkOptions(SystemParamEnum.REFERRAL_LINK_SOURCE).stream()
				.collect(Collectors.toMap(ReferralLinkOptionResponse::getValue, Function.identity()));
			response.setSources(generateOptions(queryPairs.get(SOURCE_PARAM), sourceMap));
		}

		if (queryPairs.containsKey(MEDIUM_PARAM)) {
			Map<String, ReferralLinkOptionResponse> mediumMap = getReferralLinkOptions(SystemParamEnum.REFERRAL_LINK_MEDIUM).stream()
				.collect(Collectors.toMap(ReferralLinkOptionResponse::getValue, Function.identity()));
			response.setMediums(generateOptions(queryPairs.get(MEDIUM_PARAM), mediumMap));
		}

		if (queryPairs.containsKey(CAMPAIGN_PARAM)) {
			//separate description and date
			String campaignValue = queryPairs.get(CAMPAIGN_PARAM);
			String storeCodePrefix = referralLinkDetailResponseDto.getDescription();
			String prefix = (campaignValue.startsWith(CAMPAIGN_HKTV_ATTR) ? CAMPAIGN_HKTV_ATTR : CAMPAIGN_THE_PLACE_ATTR) + storeCodePrefix;

			if (campaignValue.equals(prefix)) {
				return response;
			}

			// remove the prefix
			String campaignString = campaignValue.substring(prefix.length() + 1); // +1 is for the UNDERSTORE

			int dateLength = DateTimeUtil.DATE_WITH_ONLY_LETTER.length();
			if (campaignString.length() < dateLength) {
				response.setCampaignDescription(campaignString);
			} else {
				if (campaignString.length() == dateLength) {
					if (isValidDate(campaignString)) {
						response.setCampaignDate(campaignString);
					} else {
						response.setCampaignDescription(campaignString);
					}
				} else {
					int idx = campaignString.lastIndexOf(UNDERSTORE);
					if (idx < 0 || campaignString.length() - idx - 1 != dateLength) {
						response.setCampaignDescription(campaignString);
					} else {
						String guessDate = campaignString.substring(idx + 1);
						if (isValidDate(guessDate)) {
							response.setCampaignDate(guessDate);
							response.setCampaignDescription(campaignString.substring(0, idx));
						} else {
							response.setCampaignDescription(campaignString);
						}
					}
				}
			}
		}

		return response;
	}

	private List<ReferralLinkOptionResponse> generateOptions(String paramString, Map<String, ReferralLinkOptionResponse> optionMap) {
		List<ReferralLinkOptionResponse> result = new ArrayList<>();
		Arrays.stream(paramString.split(StringUtils.SPACE)).forEach(data -> {
			if (optionMap.containsKey(data)) {
				result.add(optionMap.get(data));
			} else {
				result.add(ReferralLinkOptionResponse.builder()
					.name(data)
					.value(data)
					.build());
			}
		});
		return result;
	}

	private Pair<String, Map<String, String>> getUrlInfo(String url) {
		Map<String, String> queryPairs = new HashMap<>();
		URI uri;

		try {
			uri = new URI(url);
		} catch (URISyntaxException e) {
			log.error("Invalid url:{}, exception ", url, e);
			return Pair.of(null, queryPairs);
		}

		String originalUrl = uri.getScheme() + "://" + uri.getHost() + uri.getPath();
		String query = uri.getQuery();
		if (query == null) {
			return Pair.of(originalUrl, queryPairs);
		}

		String[] pairs = query.split("&");

		for (String pair : pairs) {
			int idx = pair.indexOf("=");
			String key = URLDecoder.decode(pair.substring(0, idx), StandardCharsets.UTF_8);
			String value = URLDecoder.decode(pair.substring(idx + 1), StandardCharsets.UTF_8);
			queryPairs.put(key, value);
		}

		return Pair.of(originalUrl, queryPairs);
	}

	private boolean isValidDate(String value) {
		try {
			LocalDate date = LocalDate.parse(value, DateTimeFormatter.ofPattern(DateTimeUtil.DATE_WITH_ONLY_LETTER));
			if (date.getYear() < VALID_YEAR_SINCE) {
				return false;
			}
		} catch (DateTimeParseException e) {
			return false;
		}

		return true;
	}

	private HttpHeaders getResponseHeader() {
		String fileNameFormat = "referral_link_%s.xlsx";
		String fileName = String.format(fileNameFormat, DateTimeFormatter.ofPattern(DateTimeUtil.DATE_WITH_ONLY_LETTER).format(LocalDate.now()));
		HttpHeaders header = new HttpHeaders();
		header.setContentType(new MediaType("application", "octet-stream"));
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		return header;
	}
}
