package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.AffiliateRebateSettingRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.StoreAffiliateRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo.AffiliateRebateSettingDo;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.AffiliateRebateSettingBaseDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.DefaultRebateSettingDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.MappingFileDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.SaveStoreAffiliateDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.mapper.AffiliateRebateSettingBaseDtoMapper;
import com.shoalter.mms.store.api.adapter.mms.affiliate.mapper.AffiliateRebateSettingDoMapper;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.StoreAffiliateDo;
import com.shoalter.mms.store.api.adapter.permission.PermissionAdapter;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingStatusEnum;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingTypeEnum;
import com.shoalter.mms.store.api.exception.WarningException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Service
public class SaveStoreAffiliateApiService {
    private final AffiliateAdapter affiliateAdapter;
    private final PermissionAdapter permissionAdapter;

	private final AffiliateRebateSettingRepository affiliateRebateSettingRepository;
    private final StoreAffiliateRepository storeAffiliateRepository;
    private final ObjectMapper objectMapper;
	private final AffiliateRebateSettingBaseDtoMapper affiliateRebateSettingBaseDtoMapper;
	private final AffiliateRebateSettingDoMapper affiliateRebateSettingDoMapper;

    public ResponseBodyDto<Void> start(UserDto userDto, Integer storeId, SaveStoreAffiliateDto saveStoreAffiliateDto) {
        affiliateAdapter.checkStoreExist(storeId); // check store exist
        permissionAdapter.checkAdminRole(userDto);
        start(storeId, saveStoreAffiliateDto);
        ResponseBodyDto<Void> responseBodyDto = new ResponseBodyDto<>();
        responseBodyDto.setStatus("SUCCESS");
        return responseBodyDto;
    }

    private void start(Integer storeId, SaveStoreAffiliateDto saveStoreAffiliateDto) {
        StoreAffiliateDo storeAffiliateDo = storeAffiliateRepository.findByStoreId(storeId);
        if(storeAffiliateDo == null) {
            storeAffiliateDo = new StoreAffiliateDo();
            storeAffiliateDo.setStoreId(storeId);
        }
        String platformData = null;
        if(saveStoreAffiliateDto.getMappingFile() != null) {
            checkMappingFile(saveStoreAffiliateDto.getMappingFile());
            storeAffiliateDo.setSettingType("MAPPING_FILE");
            try {
                platformData = objectMapper.writeValueAsString(saveStoreAffiliateDto.getMappingFile());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        } else {
            storeAffiliateDo.setSettingType("PLATFORM");
            switch (saveStoreAffiliateDto.getPlatformCode()) {
                case "IMPACT":
                    if(saveStoreAffiliateDto.getImpact() != null) {
                        try {
                            platformData = objectMapper.writeValueAsString(saveStoreAffiliateDto.getImpact());
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    break;

                case "TMALL":
                    if(saveStoreAffiliateDto.getTMall() != null) {
                        try {
                            platformData = objectMapper.writeValueAsString(saveStoreAffiliateDto.getTMall());
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    break;
                case "RAKUTEN":
                    if(saveStoreAffiliateDto.getRakuten() != null) {
                        try {
                            platformData = objectMapper.writeValueAsString(saveStoreAffiliateDto.getRakuten());
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    break;
                case "OPTIMISE":
                    if(saveStoreAffiliateDto.getOptimise() != null) {
                        try {
                            platformData = objectMapper.writeValueAsString(saveStoreAffiliateDto.getOptimise());
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    break;

                default:
                    throw new WarningException("affiliate.platform.does.not.exist");
            }
        }

        if(!StringUtils.hasText(platformData)) {
            throw new WarningException("affiliate.platform.setting.error");
        }
        log.info("platformData = {}", platformData);
		checkDefaultRebateSetting(saveStoreAffiliateDto.getDefaultRebateSetting());

        storeAffiliateDo.setPlatformCode(saveStoreAffiliateDto.getPlatformCode());
		if (saveStoreAffiliateDto.getDefaultRebateSetting() != null) {
			String programId = saveStoreAffiliateDto.getDefaultRebateSetting().getProgramId();
			BigDecimal rebateCap = saveStoreAffiliateDto.getDefaultRebateSetting().getRebateCap() != null
				? saveStoreAffiliateDto.getDefaultRebateSetting().getRebateCap().setScale(2, RoundingMode.DOWN) : null;
			checkRebateCap(rebateCap);
			storeAffiliateDo.setProgramId(programId);
			storeAffiliateDo.setRebateCap(rebateCap);
		}
        storeAffiliateDo.setPlatformData(platformData);
        storeAffiliateRepository.save(storeAffiliateDo);

		Optional<AffiliateRebateSettingDo> defaultRebateSettingDo = affiliateRebateSettingRepository.findFirstByStoreIdAndRebateSettingTypeAndRebateSettingStatus(storeId, AffiliateRebateSettingTypeEnum.DEFAULT, AffiliateRebateSettingStatusEnum.ACTIVE);
		if (saveStoreAffiliateDto.getDefaultRebateSetting() == null) {
			defaultRebateSettingDo.ifPresent(defaultRebateSetting -> {
				checkToggleOffRestrictions(storeId);
				defaultRebateSetting.setRebateSettingStatus(AffiliateRebateSettingStatusEnum.INACTIVE);
				affiliateRebateSettingRepository.save(defaultRebateSetting);

				List<AffiliateRebateSettingDo> scheduleRebateList = affiliateRebateSettingRepository.findAllByStoreIdAndRebateSettingTypeAndRebateSettingStatusAndRebateStartDateGreaterThan(storeId, AffiliateRebateSettingTypeEnum.SCHEDULED, AffiliateRebateSettingStatusEnum.ACTIVE, LocalDateTime.now());
				scheduleRebateList.forEach(scheduleRebate -> {
					scheduleRebate.setRebateSettingStatus(AffiliateRebateSettingStatusEnum.INACTIVE);
					affiliateRebateSettingRepository.save(scheduleRebate);
				});
			});
		} else {
			AffiliateRebateSettingDo rebateSettingDo = affiliateRebateSettingDoMapper.fromDefaultRebateSettingDto(saveStoreAffiliateDto.getDefaultRebateSetting(), storeId, AffiliateRebateSettingStatusEnum.ACTIVE);
			defaultRebateSettingDo.ifPresent(defaultRebateSetting -> {
				rebateSettingDo.setId(defaultRebateSetting.getId());
				rebateSettingDo.setCreatedTime(defaultRebateSetting.getCreatedTime());
			});
			affiliateRebateSettingRepository.save(rebateSettingDo);
		}

    }

	private void checkDefaultRebateSetting(DefaultRebateSettingDto defaultRebateSettingDto) {
		if (defaultRebateSettingDto == null) {
			return;
		}
		if (!StringUtils.hasText(defaultRebateSettingDto.getProgramId())) {
			throw new WarningException("brand.id.must.fill.in");
		}
		AffiliateRebateSettingBaseDto affiliateRebateSettingBaseDto = affiliateRebateSettingBaseDtoMapper.fromDefaultRebateSettingDto(defaultRebateSettingDto);
		affiliateAdapter.checkBaseRebateSetting(affiliateRebateSettingBaseDto);
	}

    private void checkMappingFile(MappingFileDto mappingFileDto) {
        if(!(StringUtils.hasText(mappingFileDto.getZhFeedUrlMappingColumn()) ||
                StringUtils.hasText(mappingFileDto.getEnFeedUrlMappingColumn()))
        ) {
            throw new WarningException("affiliate.platform.setting.error");
        }
    }

	private void checkToggleOffRestrictions(Integer storeId) {
		Optional<AffiliateRebateSettingDo> affiliateRebateSettingDo = affiliateRebateSettingRepository.findByRebateSettingTypeAndRebateSettingStatusAndStoreIdWithinDateRange(AffiliateRebateSettingTypeEnum.SCHEDULED, AffiliateRebateSettingStatusEnum.ACTIVE, storeId, LocalDateTime.now());
		if (affiliateRebateSettingDo.isPresent()) {
			throw new WarningException("cannot.toggle.off.in.schedule.rebate.period");
		}
	}

	private void checkRebateCap(BigDecimal rebateCap) {
		if (rebateCap != null && rebateCap.compareTo(BigDecimal.ZERO) < 0) {
			throw new WarningException("rebate.cap.cannot.be.less.than.zero");
		}
	}
}
