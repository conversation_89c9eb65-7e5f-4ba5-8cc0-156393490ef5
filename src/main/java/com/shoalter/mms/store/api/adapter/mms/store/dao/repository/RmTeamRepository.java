package com.shoalter.mms.store.api.adapter.mms.store.dao.repository;

import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.RmTeamEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RmTeamRepository extends JpaRepository<RmTeamEntity, Integer> {

    @Query("SELECT rt FROM RmTeamEntity rt WHERE rt.teamLeaderId = :teamLeaderId")
    List<RmTeamEntity> findByTeamLeaderId(@Param("teamLeaderId") Integer teamLeaderId);

	@Query("SELECT rt FROM RmTeamEntity rt WHERE rt.srmId = :srmId")
	List<RmTeamEntity> findBySrmId(@Param("srmId") Integer srmId);

    @Query("SELECT rt FROM RmTeamEntity rt WHERE rt.deptCode = (SELECT r.deptCode FROM RmTeamEntity r WHERE r.userId = :rmoUserId)")
    List<RmTeamEntity> findRmsByRmoDeptCode(@Param("rmoUserId") Integer rmoUserId);

    @Query("SELECT rt FROM RmTeamEntity rt WHERE rt.deptHeadId = :deptHeadId")
    List<RmTeamEntity> findByDeptHeadId(@Param("deptHeadId") Integer deptHeadId);
}
