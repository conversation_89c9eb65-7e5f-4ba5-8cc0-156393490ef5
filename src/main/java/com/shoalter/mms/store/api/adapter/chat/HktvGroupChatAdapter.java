package com.shoalter.mms.store.api.adapter.chat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms.store.api.adapter.chat.dto.CreateChatGroupRequestDto;
import com.shoalter.mms.store.api.adapter.chat.dto.GroupChatResponseDto;
import com.shoalter.mms.store.api.adapter.chat.dto.UpdateChatGroupUserRequestDto;
import com.shoalter.mms.store.api.adapter.http.HttpGateway;
import com.shoalter.mms.store.api.adapter.http.dto.HttpRequestDto;
import com.shoalter.mms.store.api.adapter.http.exception.HttpException;
import com.shoalter.mms.store.api.config.security.GroupChatPrivateKeyConfig;
import com.shoalter.mms.store.api.constant.Constant;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.HttpStatusCodeException;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Date;
import java.util.Optional;

@Slf4j
@Validated
@RequiredArgsConstructor
@Component
public class HktvGroupChatAdapter {
	private final HttpGateway httpGateway;
	private final ObjectMapper objectMapper;
	private final GroupChatPrivateKeyConfig groupChatPrivateKeyConfig;

	@Value("${hktv-group-chat.base-url}")
	private String hktvGroupChatBaseUrl;

	private final String hktvGroupChatCreateGroupEndpoint = "/api/internal/group";
	private final String hktvGroupChatUpdateGroupUserEndpoint = "/api/internal/group/admins";


	public Optional<GroupChatResponseDto> createGroup(CreateChatGroupRequestDto requestDto) throws NoSuchAlgorithmException, InvalidKeySpecException {
		var request =
			HttpRequestDto.<CreateChatGroupRequestDto, GroupChatResponseDto>builder()
				.url(hktvGroupChatBaseUrl + hktvGroupChatCreateGroupEndpoint)
				.method(HttpMethod.POST)
				.headers(getGroupChatHeaders())
				.body(requestDto)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();

		return exchangeForBody(request);
	}

	public Optional<GroupChatResponseDto> updateGroupUser(UpdateChatGroupUserRequestDto requestDto) throws NoSuchAlgorithmException, InvalidKeySpecException {
		var request =
			HttpRequestDto.<UpdateChatGroupUserRequestDto, GroupChatResponseDto>builder()
				.url(hktvGroupChatBaseUrl + hktvGroupChatUpdateGroupUserEndpoint)
				.method(HttpMethod.PUT)
				.headers(getGroupChatHeaders())
				.body(requestDto)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}


	public HttpHeaders getGroupChatHeaders() throws NoSuchAlgorithmException, InvalidKeySpecException {
		String token = Jwts.builder()
			.setSubject("GROUP_CHAT")
			.claim("role", "SYSTEM")
			.setIssuedAt(new Date())
			.setExpiration(new Date(System.currentTimeMillis() + Constant.JWT_EXPIRATION))
			.signWith(SignatureAlgorithm.RS256, groupChatPrivateKeyConfig.privateKey())
			.compact();

		var headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(HttpHeaders.AUTHORIZATION, Constant.BEARER_SCHEME + token);
		return headers;
	}

	private <T> Optional<GroupChatResponseDto> exchangeForBody(HttpRequestDto<T, GroupChatResponseDto> request) {
		try {
			return httpGateway.exchangeForBody(request);
		} catch (HttpException e) {
			if (e.getCause() instanceof HttpStatusCodeException) {
				HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) e.getCause();
				if (httpStatusCodeException.getStatusCode().is4xxClientError()) {
					try {
						GroupChatResponseDto response = objectMapper.readValue(httpStatusCodeException.getResponseBodyAsString(), new TypeReference<>() {
						});
						log.error(e.getMessage(), e);
						return Optional.ofNullable(response);
					} catch (JsonProcessingException ex) {
						throw e;
					}
				}
			}
			throw e;
		}
	}

}
