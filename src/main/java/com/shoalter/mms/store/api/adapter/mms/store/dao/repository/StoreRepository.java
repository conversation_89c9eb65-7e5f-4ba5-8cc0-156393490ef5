package com.shoalter.mms.store.api.adapter.mms.store.dao.repository;

import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.StoreEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.StoreContractDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.StoreOpenApiDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.StorePaymentGroupInfoDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.StoreQueryDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.SyncStoreDo;
import com.shoalter.mms.store.api.adapter.mms.store.dto.QueryStoresDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface StoreRepository extends JpaRepository<StoreEntity,Integer> {
	Optional<StoreEntity> findByStoreCode(String storeCode);

	Optional<StoreEntity> findByStorefrontStoreCode(String storeFrontStoreCode);

	Optional<StoreEntity> findFirstByStorefrontStoreCode(String storeFrontStoreCode);

	@Query(value = "select s.ID         as storeId, " +
		"       s.STORE_NAME            as storeNameEn, " +
		"       i.URL            		as storeLogo, " +
		"       s.STORE_NAME_TC         as storeNameTc, " +
		"       s.STOREFRONT_STORE_CODE as storeFrontStoreCode, " +
		"       s.PACKAGE_COLOR         as packageColor, " +
		"       s.STORE_TOKEN           as storeToken, " +
		"       s.AUTO_COMPLETE_ORDER   as autoCompleteOrder, " +
		"       s.CREATED_DATE   		as createdDate, " +
		"       s.Active_ind   			as activeInd, " +
		"       m.ID   					as merchantId, " +
		"       m.MERCHANT_NAME   		as merchantNameEn, " +
		"       m.MERCHANT_NAME_TCHI   	as merchantNameCh, " +
		"       bu.CODE                 as busUnitCode " +
		"from STORE s " +
		"         join MERCHANT_STORE ms on ms.Store_id = s.Id " +
		"         join MERCHANT m on ms.MERCHANT_id = m.ID " +
		"         join BUS_UNIT bu on bu.ID = ms.BUS_UNIT_ID " +
		"         join PLATFORM p on p.ID = bu.PLATFORM_ID " +
		"         left join IMAGE i on TABLE_ID = s.Id AND i.TYPE = 'storeLogo' AND i.REFERENCE_TABLE = 'store' " +
		"where bu.TYPE = 'eCommerce' " +
		"  and (:storeActiveInd is null or s.Active_ind = :storeActiveInd) " +
		"  and bu.CODE in :#{#queryStoresDto.buCodes} " +
		"  and (:#{#queryStoresDto.merchantId} is null or m.id = :#{#queryStoresDto.merchantId}) " +
		"and (:#{#queryStoresDto.queryUserId} is null or IF((select count(us.ID) from USER_STORE us WHERE us.USER_ID = :#{#queryStoresDto.queryUserId}) > 0, " +
		" ms.Store_id in (select us.Store_id from USER_STORE us WHERE us.USER_ID = :#{#queryStoresDto.queryUserId} and us.STORE_ID = ms.Store_id), 1)) " +
		"and ((:#{#queryStoresDto.roleType} = 'S' or m.ID = :#{#queryStoresDto.merchantId})) " +
		"and (:#{#queryStoresDto.queryRoleCode} is null or " +
		"(:#{#queryStoresDto.queryRoleCode} in ('RM') and m.ID in ( " +
		"    SELECT distinct um.MERCHANT_ID " +
		"    FROM USER_MERCHANT um " +
		"    WHERE um.USER_ID = :#{#queryStoresDto.rmUserId} " +
		")) or " +
		"(:#{#queryStoresDto.queryRoleCode} in ('SRM') and m.ID in ( " +
		"    SELECT distinct um.MERCHANT_ID " +
		"    FROM USER_MERCHANT um " +
		"    JOIN RM_TEAM rt ON um.USER_ID = rt.USER_ID " +
		"    WHERE rt.SRM_ID = :#{#queryStoresDto.rmUserId} OR rt.USER_ID = :#{#queryStoresDto.rmUserId}" +
		")) or " +
		"(:#{#queryStoresDto.queryRoleCode} in ('RML', 'RMO', 'DEPT_HEAD') and m.ID in ( " +
		"    SELECT distinct um.MERCHANT_ID " +
		"    FROM USER_MERCHANT um " +
		"    JOIN RM_TEAM rt ON um.USER_ID = rt.USER_ID " +
		"    WHERE CASE :#{#queryStoresDto.queryRoleCode} " +
		"    WHEN 'RML' THEN rt.TEAM_LEADER_ID = :#{#queryStoresDto.rmUserId} " +
		"    WHEN 'RMO' THEN rt.DEPT_CODE in (SELECT RMO.DEPT_CODE FROM RM_TEAM RMO WHERE RMO.USER_ID = :#{#queryStoresDto.rmUserId}) " +
		"    WHEN 'DEPT_HEAD' THEN rt.DEPT_HEAD_ID = :#{#queryStoresDto.rmUserId} " +
		"    END " +
		")))", nativeQuery = true)
	List<StoreQueryDo> findByBuAndUser(@Param("queryStoresDto") QueryStoresDto queryStoresDto, String storeActiveInd);

	@Transactional
	@Modifying
	@Query(value = "update STORE set AUTO_COMPLETE_ORDER = :isAutoCompleteOrder where STOREFRONT_STORE_CODE = :storeFrontStoreCode", nativeQuery = true)
	int updateAutoCompleteOrderByStoreFrontStoreCode(boolean isAutoCompleteOrder, String storeFrontStoreCode);

	@Transactional
	@Modifying
	@Query(value = "update STORE set ENABLE_CUSTOMER_CHAT = :enableCustomerChat, LAST_UPDATED_BY = :updatedBy, LAST_UPDATED_DATE = NOW() where ID = :storeId", nativeQuery = true)
	int updateEnableCustomerChat(Integer storeId, boolean enableCustomerChat, String updatedBy);

	@Query(value = "select count(*) " +
			"from STORE s " +
			"inner join MERCHANT_STORE ms on ms.Store_id = s.Id " +
			"inner join USER_MERCHANT um on um.MERCHANT_ID = ms.MERCHANT_id " +
			"where s.id = :storeId and um.USER_ID = :userId", nativeQuery = true)
	int countByStoreIdAndRmUserId(Integer storeId, Integer userId);

	@Query(value = "select count(*) from ( " +
			"SELECT distinct s.* " +
			"from STORE s " +
			"inner join MERCHANT_STORE ms on ms.Store_id = s.Id " +
			"INNER JOIN USER_MERCHANT um on ms.MERCHANT_id = um.MERCHANT_ID " +
			"INNER JOIN RM_TEAM rt on um.USER_ID = rt.USER_ID " +
			"WHERE rt.TEAM_LEADER_ID = :userId and s.ID = :storeId " +
			"Union " +
			"SELECT distinct s.* " +
			"from STORE s " +
			"inner join MERCHANT_STORE ms on ms.Store_id = s.Id " +
			"INNER JOIN USER_MERCHANT um on ms.MERCHANT_id = um.MERCHANT_ID " +
			"WHERE um.USER_ID = :userId and s.ID = :storeId " +
			") M", nativeQuery = true)
	int countByStoreIdAndRmlUserId(Integer storeId, Integer userId);

	@Query(value = "select count(1) " +
			"from STORE s " +
			"inner join MERCHANT_STORE ms on ms.Store_id = s.Id " +
			"inner join USER_MERCHANT um on um.MERCHANT_ID = ms.MERCHANT_id " +
			"inner join RM_TEAM rt on rt.USER_ID = um.USER_ID " +
			"where rt.DEPT_CODE in (select DEPT_CODE from RM_TEAM RMO where USER_ID = :userId) " +
			"and s.id = :storeId", nativeQuery = true)
	int countByStoreIdAndRmTeamUserId(Integer storeId, Integer userId);

	@Query(value = "select * from STORE " +
		"where Id in ( " +
		"    select STORE_ID from STORE_AFFILIATE " +
		")", nativeQuery = true)
	List<StoreEntity> findAffiliateStoreList();

	@Query(value = "SELECT S.id                 AS storeId, " +
		"       S.STOREFRONT_STORE_CODE         AS storefrontStoreCode, " +
		"       BU.CODE                         AS buCode, " +
		"       MS.MERCHANT_id                  AS merchantId, " +
		"       S.STORE_NAME                    AS storeNameEn, " +
		"       S.STORE_NAME_TC                 AS storeNameZh, " +
		"       S.Active_ind                    AS storeEnable, " +
		"       S.ENABLE_CUSTOMER_CHAT          AS customerChatEnable, " +
		"       GROUP_CONCAT(DISTINCT C.STATUS) AS contractStatus, " +
		"       I.URL                           as storeLogo " +
		"FROM STORE S " +
		"         JOIN BUS_UNIT BU ON S.BUS_UNIT_ID = BU.ID AND BU.CODE in :bucodes " +
		"         JOIN MERCHANT_STORE MS ON MS.Store_id = S.Id " +
		"         JOIN CONTRACT C ON C.STORE_ID = S.Id and C.MASTER_CONTRACT_ID is null " +
		"         LEFT JOIN IMAGE I ON TABLE_ID = S.Id AND I.TYPE = 'storeLogo' AND I.REFERENCE_TABLE = 'store' " +
		"WHERE S.STOREFRONT_STORE_CODE IN :storeFrontStoreCodes " +
		"group by S.Id, MS.MERCHANT_id, I.URL",
		nativeQuery = true)
	List<SyncStoreDo> findByStorefrontStoreCodesAndBuCodes(List<String> storeFrontStoreCodes, Set<String> bucodes);

	@Query(value = "SELECT S.*" +
		"FROM STORE S " +
		"         JOIN BUS_UNIT BU ON S.BUS_UNIT_ID = BU.ID AND BU.CODE = :buCode " +
		"WHERE S.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
		"LIMIT 1",
		nativeQuery = true)
	Optional<StoreEntity> findByStorefrontStoreCodeAndBuCode(String storefrontStoreCode, String buCode);

	@Query(value = "SELECT S.id                 AS storeId, " +
		"       S.STOREFRONT_STORE_CODE         AS storefrontStoreCode, " +
		"       BU.CODE                         AS buCode, " +
		"       S.STORE_NAME                    AS storeNameEn, " +
		"       S.STORE_NAME_TC                 AS storeNameTc, " +
		"       S.Active_ind                    AS activeInd " +
		"FROM STORE S " +
		"         JOIN BUS_UNIT BU ON S.BUS_UNIT_ID = BU.ID AND BU.CODE in :bucodes " +
		"WHERE S.STOREFRONT_STORE_CODE IN :storeFrontStoreCodes ",
		nativeQuery = true)
	List<StoreQueryDo> findStoreInfoByStorefrontStoreCodesAndBuCodes(List<String> storeFrontStoreCodes, Set<String> bucodes);

	@Query(value = "select s.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"       		   s.BUS_UNIT_ID           as busUnitId, " +
		"       		   s.STORE_NAME            as storeNameEn, " +
		"       		   s.STORE_NAME_TC         as storeNameCh, " +
		"       		   m.ID                    as merchantId, " +
		"       		   m.MERCHANT_NAME         as merchantNameEn, " +
		"       		   m.MERCHANT_NAME_SCHI    as merchantNameCh, " +
		"       		   s.Active_ind            as activeInd, " +
		"       		   s.PAYMENT_GROUP_CODE    as paymentGroupCode, " +
		"       		   sp.SHORT_DESC           as paymentGroupDescription " +
		"from STORE s " +
		"         join MERCHANT_STORE ms on ms.Store_id = s.Id " +
		"         join MERCHANT m on m.ID = ms.MERCHANT_id " +
		"         left join SYS_PARM sp on sp.SEGMENT = 'PAYMENT_GROUP' and sp.CODE = s.PAYMENT_GROUP_CODE" +
		"		  where s.STOREFRONT_STORE_CODE in :storeFrontStoreCodes",
		nativeQuery = true)
	List<StorePaymentGroupInfoDo> findStorePaymentGroupInfoByStorefrontStoreCodes(List<String> storeFrontStoreCodes);

	@Query(value = "select distinct s.STOREFRONT_STORE_CODE " +
		"from STORE s " +
		"         join CONTRACT c on c.STORE_ID = s.Id and c.MASTER_CONTRACT_ID is null " +
		"where c.STATUS in :contractStatus " +
		"  and s.Id in :storeIds",
		nativeQuery = true)
	Set<String> filterStoreByStoreIdAndContractStatus(List<Integer> storeIds, Set<String> contractStatus);

	@Query(value = "SELECT S.id                 AS storeId, " +
		"       S.STOREFRONT_STORE_CODE         AS storefrontStoreCode, " +
		"       GROUP_CONCAT(DISTINCT C.STATUS) AS contractStatus " +
		"FROM STORE S " +
		"         JOIN CONTRACT C ON C.STORE_ID = S.Id and C.MASTER_CONTRACT_ID is null " +
		"WHERE S.Id IN :storeIds " +
		"group by S.Id",
		nativeQuery = true)
	List<StoreContractDo> findStoreContractStatusByStoreIds(List<Integer> storeIds);

	@Query(value = "SELECT S.API_KEY as apiKey, S.API_KEY_SECRET as apiSecretKey " +
		"FROM STORE S " +
		"JOIN BUS_UNIT BU ON BU.ID = S.BUS_UNIT_ID AND BU.TYPE = :busUnitType " +
		"JOIN PLATFORM P ON P.ID = BU.PLATFORM_ID AND P.PLATFORM_CODE = :buCode " +
		"WHERE S.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
		"LIMIT 1", nativeQuery = true)
	StoreOpenApiDo getStoreOpenApiKey(String busUnitType, String buCode, String storefrontStoreCode);

	List<StoreEntity> findAllByIdIn(List<Integer> storeIds);
}
