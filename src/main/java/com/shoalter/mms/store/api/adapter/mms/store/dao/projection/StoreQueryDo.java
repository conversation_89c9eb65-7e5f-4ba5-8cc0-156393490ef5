package com.shoalter.mms.store.api.adapter.mms.store.dao.projection;

import java.util.Date;

public interface StoreQueryDo {
	Integer getStoreId();

	String getStoreNameEn();

	String getStoreNameTc();

	String getStoreLogo();

	String getStorefrontStoreCode();

	String getPackageColor();

	String getStoreToken();

	String getBusUnitCode();

	String getActiveInd();

	Integer getMerchantId();

	String getMerchantNameEn();

	String getMerchantNameCh();

	boolean isAutoCompleteOrder();

	Date getCreatedDate();

	String getBuCode();
}

