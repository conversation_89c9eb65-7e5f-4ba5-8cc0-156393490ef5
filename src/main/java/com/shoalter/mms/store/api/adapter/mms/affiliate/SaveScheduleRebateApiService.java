package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.AffiliateRebateSettingRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo.AffiliateRebateSettingDo;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.AffiliateRebateSettingBaseDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dto.ScheduleRebateSettingDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.mapper.AffiliateRebateSettingBaseDtoMapper;
import com.shoalter.mms.store.api.adapter.mms.affiliate.mapper.AffiliateRebateSettingDoMapper;
import com.shoalter.mms.store.api.adapter.permission.PermissionAdapter;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingStatusEnum;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingTypeEnum;
import com.shoalter.mms.store.api.exception.WarningException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@RequiredArgsConstructor
@Service
public class SaveScheduleRebateApiService {
	private final AffiliateAdapter affiliateAdapter;
	private final PermissionAdapter permissionAdapter;
	private final AffiliateRebateSettingRepository affiliateRebateSettingRepository;
	private final AffiliateRebateSettingBaseDtoMapper affiliateRebateSettingBaseDtoMapper;
	private final AffiliateRebateSettingDoMapper affiliateRebateSettingDoMapper;

	public ResponseBodyDto<Void> start(UserDto userDto, Integer storeId, ScheduleRebateSettingDto scheduleRebateSettingDto) {
		permissionAdapter.checkAdminRole(userDto);
		affiliateAdapter.checkStoreExist(storeId);
		affiliateAdapter.checkDefaultRebateSettingExist(storeId);
		checkParameter(scheduleRebateSettingDto, storeId);
		AffiliateRebateSettingDo affiliateRebateSettingDo = affiliateRebateSettingDoMapper.fromScheduleRebateSettingDto(scheduleRebateSettingDto, storeId, AffiliateRebateSettingStatusEnum.ACTIVE);
		affiliateRebateSettingRepository.save(affiliateRebateSettingDo);

		ResponseBodyDto<Void> responseBodyDto = new ResponseBodyDto<>();
		responseBodyDto.setStatus("SUCCESS");
		return responseBodyDto;
	}

	private void checkParameter(ScheduleRebateSettingDto scheduleRebateSettingDto, Integer storeId) {
		checkRequired(scheduleRebateSettingDto);

		LocalDateTime tomorrow = LocalDate.now().plusDays(1).atStartOfDay();
		LocalDateTime rebateStartDate = scheduleRebateSettingDto.getRebateStartDate().truncatedTo(ChronoUnit.HOURS);
		LocalDateTime rebateEndDate = scheduleRebateSettingDto.getRebateEndDate().truncatedTo(ChronoUnit.HOURS);
		if (rebateStartDate.isBefore(tomorrow)) {
			throw new WarningException("start.date.must.be.greater.than.today");
		}

		if (rebateStartDate.isAfter(rebateEndDate)) {
			throw new WarningException("end.date.greater.than.start.date");
		}

		if (isDateRangeOverlapping(rebateStartDate, rebateEndDate, storeId)) {
			throw new WarningException("start.end.date.range.overlap");
		}

		AffiliateRebateSettingBaseDto affiliateRebateSettingBaseDto = affiliateRebateSettingBaseDtoMapper.fromScheduleRebateSettingDto(scheduleRebateSettingDto);
		affiliateAdapter.checkBaseRebateSetting(affiliateRebateSettingBaseDto);
	}

	private void checkRequired(ScheduleRebateSettingDto scheduleRebateSettingDto) {
		if (scheduleRebateSettingDto.getRebateStartDate() == null
			|| scheduleRebateSettingDto.getRebateEndDate() == null
			|| scheduleRebateSettingDto.getTierOneThreshold() == null
			|| scheduleRebateSettingDto.getTierOneRebateRate() == null) {
			throw new WarningException("schedule.rebate.required.setting.missing");
		}
	}

	private boolean isDateRangeOverlapping(LocalDateTime startDate, LocalDateTime endDate, Integer storeId) {
		List<AffiliateRebateSettingDo> existScheduleRebateList = affiliateRebateSettingRepository.findAllByStoreIdAndRebateSettingTypeAndRebateSettingStatusAndRebateEndDateGreaterThan(storeId, AffiliateRebateSettingTypeEnum.SCHEDULED, AffiliateRebateSettingStatusEnum.ACTIVE, LocalDateTime.now());
		return existScheduleRebateList.stream()
			.anyMatch(scheduleRebate ->
				!(endDate.isBefore(scheduleRebate.getRebateStartDate()) || startDate.isAfter(scheduleRebate.getRebateEndDate())));
	}
}
