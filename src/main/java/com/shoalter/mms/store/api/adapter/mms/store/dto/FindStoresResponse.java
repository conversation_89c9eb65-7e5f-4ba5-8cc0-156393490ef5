package com.shoalter.mms.store.api.adapter.mms.store.dto;

import com.shoalter.mms.store.api.adapter.chat.dto.MdbVirtualStoreResponseDto;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.SystemParamEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.StoreQueryDo;
import com.shoalter.mms.store.api.constant.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class FindStoresResponse {
	private Integer storeId;
	private String storeName;
	private String storeLogo;
	private String storeFrontStoreCode;
	private String busUnitCode;
	private String storeToken;
	private String packageColor;
	@Schema(description = "Y/P/N")
	private String activeInd;
	private boolean autoCompleteOrder;
	private Long createdDate;
	private Map<String, String> name;
	private boolean virtualStore;
	private String level;


	public static FindStoresResponse generate(StoreQueryDo storeQueryDo) {
		return FindStoresResponse.builder()
			.storeId(storeQueryDo.getStoreId())
			.storeName(storeQueryDo.getStoreNameEn())
			.storeLogo(storeQueryDo.getStoreLogo())
			.storeFrontStoreCode(storeQueryDo.getStorefrontStoreCode())
			.busUnitCode(storeQueryDo.getBusUnitCode())
			.storeToken(storeQueryDo.getStoreToken())
			.packageColor(storeQueryDo.getPackageColor())
			.autoCompleteOrder(storeQueryDo.isAutoCompleteOrder())
			.createdDate(storeQueryDo.getCreatedDate() == null ? null : storeQueryDo.getCreatedDate().getTime())
			.activeInd(storeQueryDo.getActiveInd())
			.name(new HashMap<>() {{
				put(Constant.LANG_EN, storeQueryDo.getStoreNameEn());
				put(Constant.LANG_HK, StringUtils.isNotBlank(storeQueryDo.getStoreNameTc())
					? storeQueryDo.getStoreNameTc() : storeQueryDo.getStoreNameEn());
			}})
			.virtualStore(false)
			.build();
	}

	public static FindStoresResponse generateVirtualStore(MdbVirtualStoreResponseDto virtualStore, List<SystemParamEntity> virtualStoreSysParamList) {
		SystemParamEntity virtualStoreSysParam = null;
		if (virtualStoreSysParamList != null && !virtualStoreSysParamList.isEmpty()) {
			virtualStoreSysParam = virtualStoreSysParamList.get(0);
		}
		// if sysParam exist this virtualStore, use it shortDesc, else use storeCode
		String storeNameEn = virtualStoreSysParam != null ? virtualStoreSysParam.getShortDesc() : virtualStore.getStoreCode();
		String storeNameTc = virtualStoreSysParam != null ? virtualStoreSysParam.getShortDescTc() : virtualStore.getStoreCode();

		return FindStoresResponse.builder()
			.storeName(storeNameEn)
			.storeFrontStoreCode(virtualStore.getStoreCode())
			.level(virtualStore.getLevel())
			.name(Map.of(Constant.LANG_EN, storeNameEn, Constant.LANG_HK, storeNameTc))
			.virtualStore(true)
			.build();
	}
}
