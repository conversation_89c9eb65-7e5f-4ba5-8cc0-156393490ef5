package com.shoalter.mms.store.api.adapter.chat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms.store.api.adapter.chat.dto.MdbVirtualStoreResponseDto;
import com.shoalter.mms.store.api.adapter.http.HttpGateway;
import com.shoalter.mms.store.api.adapter.http.dto.HttpRequestDto;
import com.shoalter.mms.store.api.adapter.http.exception.HttpException;
import com.shoalter.mms.store.api.adapter.mms.helper.MmsTokenHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.HttpStatusCodeException;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Validated
@RequiredArgsConstructor
@Component
public class MdbAdapter {

	private final HttpGateway httpGateway;
	private final ObjectMapper objectMapper;
	private final MmsTokenHelper mmsTokenHelper;

	@Value("${mdb.virtual-store-url}")
	private String mdbVirtualStoreUrl;

	public List<MdbVirtualStoreResponseDto> getVirtualStoreLevel(List<String> virtualStoreCodes) {
		List<MdbVirtualStoreResponseDto> mdbVirtualStoreResponseDtos = new ArrayList<>();
		try {
			HashMap<String, List<String>> requestBody = new HashMap<>();
			requestBody.put("virtual_store_code", virtualStoreCodes);

			var request =
				HttpRequestDto.<HashMap<String, List<String>>, Map<String, MdbVirtualStoreResponseDto>>builder()
					.url(mdbVirtualStoreUrl)
					.method(HttpMethod.POST)
					.headers(getMdbHeaders())
					.body(requestBody)
					.resultTypeReference(new ParameterizedTypeReference<>() {
					})
					.build();
			mdbVirtualStoreResponseDtos = exchangeForBody(request);
		} catch (Exception e) {
			log.error("getVirtualStoreLevel error: {}", e.getMessage(), e);
		}
		return mdbVirtualStoreResponseDtos;
	}

	public HttpHeaders getMdbHeaders() {
		HttpHeaders headers = new HttpHeaders();
		String accessToken = mmsTokenHelper.generateSystemMdbToken();
		headers.setBearerAuth(accessToken);
		headers.setContentType(MediaType.APPLICATION_JSON);
		return headers;
	}

	private List<MdbVirtualStoreResponseDto> exchangeForBody(HttpRequestDto<HashMap<String, List<String>>, Map<String, MdbVirtualStoreResponseDto>> request) {
		try {
			List<MdbVirtualStoreResponseDto> mdbVirtualStoreResponseDtos = new ArrayList<>();
			Optional<Map<String, MdbVirtualStoreResponseDto>> response = httpGateway.exchangeForBody(request);
			if (response.isPresent()) {
				Map<String, MdbVirtualStoreResponseDto> responseMap = response.get();

				responseMap.keySet().forEach(key -> {
					MdbVirtualStoreResponseDto mdbVirtualStoreResponseDto = new MdbVirtualStoreResponseDto();
					long endDate = responseMap.get(key).getEndDate();
					long nowDate = new Date().getTime();
					if (endDate > nowDate) {
						// key = storeCode
						mdbVirtualStoreResponseDto.setStoreCode(key);
						mdbVirtualStoreResponseDto.setLevel(responseMap.get(key).getLevel());
						mdbVirtualStoreResponseDto.setEndDate(endDate);
						mdbVirtualStoreResponseDtos.add(mdbVirtualStoreResponseDto);
					}
				});
			}
			return mdbVirtualStoreResponseDtos;
		} catch (HttpException e) {
			if (e.getCause() instanceof HttpStatusCodeException) {
				HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) e.getCause();
				if (httpStatusCodeException.getStatusCode().is4xxClientError()) {
					try {
						String response = objectMapper.readValue(httpStatusCodeException.getResponseBodyAsString(), new TypeReference<>() {
						});
						log.error(e.getMessage(), response);
					} catch (JsonProcessingException ex) {
						throw e;
					}
				}
			}
			throw e;
		}
	}
}
