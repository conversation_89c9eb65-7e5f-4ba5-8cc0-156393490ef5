package com.shoalter.mms.store.api.adapter.mms.user.dao.repository;

import com.shoalter.mms.store.api.adapter.mms.user.dao.entity.UserMerchantDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface UserMerchantRepository extends JpaRepository<UserMerchantDo, Integer> {
	@Query(value = "select count(*) " +
		"from USER_MERCHANT um " +
		"where um.MERCHANT_ID = :merchantId and um.USER_ID = :userId", nativeQuery = true)
	int countByMerchantIdAndRmUserId(Integer merchantId, Integer userId);

	@Query(value = "select count(*) from ( " +
		"SELECT distinct um.* " +
		"from USER_MERCHANT um " +
		"INNER JOIN RM_TEAM rt on um.USER_ID = rt.USER_ID " +
		"WHERE rt.TEAM_LEADER_ID = :userId and um.MERCHANT_ID = :merchantId " +
		"Union " +
		"SELECT distinct um.* " +
		"from USER_MERCHANT um " +
		"WHERE um.USER_ID = :userId and um.MERCHANT_ID = :merchantId " +
		") M",
		nativeQuery = true)
	int countByMerchantIdAndRmlUserId(Integer merchantId, Integer userId);

	@Query(value = "select count(*) " +
		"from USER_MERCHANT um " +
		"inner join RM_TEAM rt on rt.USER_ID = um.USER_ID " +
		"where rt.DEPT_CODE in (select DEPT_CODE from RM_TEAM RMO where USER_ID = :userId) and um.MERCHANT_ID = :merchantId",
		nativeQuery = true)
	int countByMerchantIdAndRmTeamUserId(Integer merchantId, Integer userId);
}
