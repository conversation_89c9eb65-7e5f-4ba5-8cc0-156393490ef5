package com.shoalter.mms.store.api.adapter.mms.affiliate;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.AffiliateRebateSettingRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.StoreAffiliateRepository;
import com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo.AffiliateRebateSettingDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.StoreAffiliateDo;
import com.shoalter.mms.store.api.adapter.permission.PermissionAdapter;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@RequiredArgsConstructor
@Service
public class DeleteStoreAffiliateApiService {

    private final AffiliateAdapter affiliateAdapter;
    private final PermissionAdapter permissionAdapter;

	private final AffiliateRebateSettingRepository affiliateRebateSettingRepository;
    private final StoreAffiliateRepository storeAffiliateRepository;

    public ResponseBodyDto<Void> start(UserDto userDto, Integer storeId) {
        affiliateAdapter.checkStoreExist(storeId); // check store exist
        permissionAdapter.checkAdminRole(userDto);
        start(storeId);
        ResponseBodyDto<Void> responseBodyDto = new ResponseBodyDto<>();
        responseBodyDto.setStatus("SUCCESS");
        return responseBodyDto;
    }

    public void start(Integer storeId) {
        StoreAffiliateDo storeAffiliateDo = storeAffiliateRepository.findByStoreId(storeId);
        if(storeAffiliateDo != null) {
            storeAffiliateRepository.delete(storeAffiliateDo);
        }

		List<AffiliateRebateSettingDo> rebateSettingDos = affiliateRebateSettingRepository.findByStoreId(storeId);
		if(!CollectionUtils.isEmpty(rebateSettingDos)) {
			rebateSettingDos.forEach(rebateSettingDo -> {
				rebateSettingDo.setRebateSettingStatus(AffiliateRebateSettingStatusEnum.INACTIVE);
				affiliateRebateSettingRepository.save(rebateSettingDo);
			});
		}
    }
}
