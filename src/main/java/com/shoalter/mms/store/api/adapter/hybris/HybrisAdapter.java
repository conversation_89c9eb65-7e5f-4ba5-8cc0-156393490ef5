package com.shoalter.mms.store.api.adapter.hybris;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms.store.api.adapter.http.HttpGateway;
import com.shoalter.mms.store.api.adapter.http.dto.HttpRequestDto;
import com.shoalter.mms.store.api.adapter.http.exception.HttpException;
import com.shoalter.mms.store.api.adapter.hybris.data.*;
import com.shoalter.mms.store.api.config.security.HybrisPrivateKeyConfig;
import com.shoalter.mms.store.api.constant.Constant;
import com.shoalter.mms.store.api.enums.BuCodeEnum;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Validated
@RequiredArgsConstructor
@Component
public class HybrisAdapter {
	private final HttpGateway httpGateway;
	private final ObjectMapper objectMapper;
	private final HybrisPrivateKeyConfig hybrisPrivateKeyConfig;

	@Value("${hybris.base-url}")
	private String hybrisBaseUrl;

	private final String getNonBunsinessDatesEndpoint = "/v1/hktv/s2s/mms/store/nonBusinessDates";
	private final String updateStoreEndpoint = "/v1/hktv/s2s/mms/updateStore";

	public Optional<HybrisResponseDto<List<HybrisNonBusinessDateResponseDto>>> getNonBusinessDates(List<String> storeCodes) throws NoSuchAlgorithmException, InvalidKeySpecException {
		UriComponentsBuilder builder = UriComponentsBuilder
			.fromHttpUrl(hybrisBaseUrl + getNonBunsinessDatesEndpoint)
			.queryParam("storeCodes", String.join(",", storeCodes));
		var request =
			HttpRequestDto.<String, HybrisResponseDto<List<HybrisNonBusinessDateResponseDto>>>builder()
				.url(builder.toUriString())
				.method(HttpMethod.GET)
				.headers(getHybrisHeaders())
				.resultTypeReference(new ParameterizedTypeReference<>() {})
				.build();

		return exchangeForBody(request);
	}

	public Optional<HybrisUpdateStoreResponseDto> updateStore(HybrisUpdateStoreRequestDto requestDto) throws NoSuchAlgorithmException, InvalidKeySpecException {
		var request = HttpRequestDto.<HybrisUpdateStoreRequestDto, HybrisUpdateStoreResponseDto>builder()
				.url(hybrisBaseUrl + updateStoreEndpoint)
				.method(HttpMethod.POST)
				.headers(getHybrisHeaders())
				.body(requestDto)
				.resultTypeReference(new ParameterizedTypeReference<>() {})
				.build();

		return exchangeForBaseBody(request);
	}

	private HttpHeaders getHybrisHeaders() throws NoSuchAlgorithmException, InvalidKeySpecException {
		String token = Jwts.builder()
			.setSubject(BuCodeEnum.HKTV.name().toLowerCase())
			.setIssuedAt(new Date())
			.setExpiration(new Date(System.currentTimeMillis() + Constant.JWT_EXPIRATION))
			.signWith(SignatureAlgorithm.RS256, hybrisPrivateKeyConfig.hybrisPrivateKey())
			.compact();

		var headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(Constant.SERVER_TOKEN, token);
		headers.add(Constant.SERVER_NAME, "mms.store");
		return headers;
	}

	private <T, R> Optional<HybrisResponseDto<R>> exchangeForBody(HttpRequestDto<T, HybrisResponseDto<R>> request) {
		try {
			return httpGateway.exchangeForBody(request);
		} catch (HttpException e) {
			if (e.getCause() instanceof HttpStatusCodeException) {
				HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) e.getCause();
				if (httpStatusCodeException.getStatusCode().is4xxClientError()) {
					try {
						HybrisResponseDto<R> response = objectMapper.readValue(httpStatusCodeException.getResponseBodyAsString(), new TypeReference<>() {
						});
						log.error(e.getMessage(), e);
						return Optional.ofNullable(response);
					} catch (JsonProcessingException ex) {
						throw e;
					}
				}
			}
			throw e;
		}
	}

	private <T, R extends HybrisBaseResponseDto> Optional<R> exchangeForBaseBody(HttpRequestDto<T, R> request) {
		try {
			return httpGateway.exchangeForBody(request);
		} catch (HttpException e) {
			if (e.getCause() instanceof HttpStatusCodeException) {
				HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) e.getCause();
				if (httpStatusCodeException.getStatusCode().is4xxClientError()) {
					try {
						R response = objectMapper.readValue(httpStatusCodeException.getResponseBodyAsString(), new TypeReference<>() {
						});
						log.error(e.getMessage(), e);
						return Optional.ofNullable(response);
					} catch (JsonProcessingException ex) {
						throw e;
					}
				}
			}
			throw e;
		}
	}

}
