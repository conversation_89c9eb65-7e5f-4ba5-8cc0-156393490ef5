package com.shoalter.mms.store.api.adapter.mms.affiliate.dao.repository.pojo;

import com.shoalter.mms.store.api.enums.AffiliateRebateSettingStatusEnum;
import com.shoalter.mms.store.api.enums.AffiliateRebateSettingTypeEnum;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "AFFILIATE_REBATE_SETTING")
public class AffiliateRebateSettingDo {
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "ID")
	private Integer id;

	@Column(name = "STORE_ID")
	private Integer storeId;

	@Column(name = "REBATE_SETTING_TYPE")
	@Enumerated(EnumType.STRING)
	private AffiliateRebateSettingTypeEnum rebateSettingType;

	@Column(name = "TIER_ONE_THRESHOLD")
	private BigDecimal tierOneThreshold;

	@Column(name = "TIER_ONE_REBATE_RATE")
	private BigDecimal tierOneRebateRate;

	@Column(name = "TIER_TWO_THRESHOLD")
	private BigDecimal tierTwoThreshold;

	@Column(name = "TIER_TWO_REBATE_RATE")
	private BigDecimal tierTwoRebateRate;

	@Column(name = "TIER_THREE_THRESHOLD")
	private BigDecimal tierThreeThreshold;

	@Column(name = "TIER_THREE_REBATE_RATE")
	private BigDecimal tierThreeRebateRate;

	@Column(name = "TIER_FOUR_THRESHOLD")
	private BigDecimal tierFourThreshold;

	@Column(name = "TIER_FOUR_REBATE_RATE")
	private BigDecimal tierFourRebateRate;

	@Column(name = "TIER_FIVE_THRESHOLD")
	private BigDecimal tierFiveThreshold;

	@Column(name = "TIER_FIVE_REBATE_RATE")
	private BigDecimal tierFiveRebateRate;

	@Column(name = "TIER_SIX_THRESHOLD")
	private BigDecimal tierSixThreshold;

	@Column(name = "TIER_SIX_REBATE_RATE")
	private BigDecimal tierSixRebateRate;

	@Column(name = "TIER_SEVEN_THRESHOLD")
	private BigDecimal tierSevenThreshold;

	@Column(name = "TIER_SEVEN_REBATE_RATE")
	private BigDecimal tierSevenRebateRate;

	@Column(name = "TIER_EIGHT_THRESHOLD")
	private BigDecimal tierEightThreshold;

	@Column(name = "TIER_EIGHT_REBATE_RATE")
	private BigDecimal tierEightRebateRate;

	@Column(name = "TIER_NINE_THRESHOLD")
	private BigDecimal tierNineThreshold;

	@Column(name = "TIER_NINE_REBATE_RATE")
	private BigDecimal tierNineRebateRate;

	@Column(name = "TIER_TEN_THRESHOLD")
	private BigDecimal tierTenThreshold;

	@Column(name = "TIER_TEN_REBATE_RATE")
	private BigDecimal tierTenRebateRate;

	@Column(name = "REBATE_START_DATE")
	private LocalDateTime rebateStartDate;

	@Column(name = "REBATE_END_DATE")
	private LocalDateTime rebateEndDate;

	@Column(name = "REBATE_SETTING_STATUS")
	@Enumerated(EnumType.STRING)
	private AffiliateRebateSettingStatusEnum rebateSettingStatus;

	@CreationTimestamp
	@Column(name = "CREATED_TIME")
	private LocalDateTime createdTime;

	@UpdateTimestamp
	@Column(name = "LAST_UPDATE_TIME")
	private LocalDateTime lastUpdateTime;
}
