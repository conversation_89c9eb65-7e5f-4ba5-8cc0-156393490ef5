package com.shoalter.mms.store.api.adapter.mms.store.usecase;

import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.ContractEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.RmTeamEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.StoreEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.ContractRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.RmTeamRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.StoreRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dto.AccessibleStoreInfoResponseDto;
import com.shoalter.mms.store.api.enums.RoleCodeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import javax.persistence.EntityManager;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
@ActiveProfiles("test")
@Import(AccessibleStoreInfoService.class)
class AccessibleStoreInfoUserCaseTest {

    @Autowired
    private AccessibleStoreInfoService accessibleStoreInfoService;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private RmTeamRepository rmTeamRepository;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private EntityManager entityManager;

    // User IDs for different roles
    private static final int DEPT_HEAD_ID = 1001;
    private static final int RMO_1_ID = 2001;
    private static final int RMO_2_ID = 2002;
    private static final int RML_1_ID = 3001;
    private static final int RML_2_ID = 3002;
    private static final int SRM_1_ID = 3501;
    private static final int SRM_2_ID = 3502;
    private static final int RM_1_ID = 4001;
    private static final int RM_2_ID = 4002;
    private static final int RM_3_ID = 4003;
    private static final int RM_4_ID = 4004;
    private static final int RM_5_ID = 4005;
    private static final int RM_6_ID = 4006;

    // Department codes
    private static final String DEPT_1 = "DEPT001";
    private static final String DEPT_2 = "DEPT002";

    private Map<String, StoreEntity> stores;
    private Map<Integer, RmTeamEntity> rmTeams;
    private Map<String, ContractEntity> contracts;

    @BeforeEach
    void setUp() {
        setupStores();
        setupRmTeams();
        setupContracts();
        saveTestData();
    }

    private void setupStores() {
        stores = new HashMap<>();

        // Create stores with different characteristics
        createStore("STORE1", "Fashion Store 1", "Y");
        createStore("STORE2", "Electronics Store", "Y");
        createStore("STORE3", "Food Store", "Y");
        createStore("STORE4", "Fashion Store 2", "Y");
        createStore("STORE5", "Beauty Store", "N"); // Inactive store
        createStore("STORE6", "Home Store", "Y");
        createStore("STORE7", "Book Store", "Y");
        createStore("STORE8", "Sports Store", "Y");
        createStore("STORE9", "Toy Store", "Y");
    }

    private void createStore(String code, String name, String activeInd) {
        StoreEntity store = new StoreEntity();
        store.setStorefrontStoreCode(code);
        store.setStoreCode("S" + code.substring(5));
        store.setStoreName(name);
        store.setActiveInd(activeInd);
        store.setCreatedDate(new Date());
        store.setLastUpdatedDate(new Date());
        stores.put(code, storeRepository.save(store));
    }

    private void setupRmTeams() {
        rmTeams = new HashMap<>();

        // Department Head's team
        createRmTeam(DEPT_HEAD_ID, "RMH001", null, null, DEPT_1);

        // RMO teams under Department 1
        createRmTeam(RMO_1_ID, "RMO001", DEPT_HEAD_ID, null, DEPT_1);

        // RML teams under RMO 1
        createRmTeam(RML_1_ID, "RML001", DEPT_HEAD_ID, RMO_1_ID, DEPT_1);

        // SRM teams under Department 1
        createRmTeam(SRM_1_ID, "SRM001", DEPT_HEAD_ID, RML_1_ID, DEPT_1);

        // RMs under RML 1 (some also under SRM 1)
        createRmTeam(RM_1_ID, "RM001", DEPT_HEAD_ID, RML_1_ID, SRM_1_ID, DEPT_1);
        createRmTeam(RM_2_ID, "RM002", DEPT_HEAD_ID, RML_1_ID, SRM_1_ID, DEPT_1);

        // Department 2 structure
        createRmTeam(RMO_2_ID, "RMO002", DEPT_HEAD_ID, null, DEPT_2);
        createRmTeam(RML_2_ID, "RML002", DEPT_HEAD_ID, RMO_2_ID, DEPT_2);

        // SRM teams under Department 2
        createRmTeam(SRM_2_ID, "SRM002", DEPT_HEAD_ID, RML_2_ID, DEPT_2);

        // RMs under RML 2 (some also under SRM 2)
        createRmTeam(RM_3_ID, "RM003", DEPT_HEAD_ID, RML_2_ID, SRM_2_ID, DEPT_2);
        createRmTeam(RM_4_ID, "RM004", DEPT_HEAD_ID, RML_2_ID, DEPT_2);

        // Additional RMs for more comprehensive testing
        createRmTeam(RM_5_ID, "RM005", DEPT_HEAD_ID, RML_1_ID, DEPT_1);
        createRmTeam(RM_6_ID, "RM006", DEPT_HEAD_ID, RML_2_ID, SRM_2_ID, DEPT_2);
    }

    private void createRmTeam(int userId, String rmCode, Integer deptHeadId, Integer teamLeaderId, String deptCode) {
        createRmTeam(userId, rmCode, deptHeadId, teamLeaderId, null, deptCode);
    }

    private void createRmTeam(int userId, String rmCode, Integer deptHeadId, Integer teamLeaderId, Integer srmId, String deptCode) {
        RmTeamEntity rmTeam = new RmTeamEntity();
        rmTeam.setUserId(userId);
        rmTeam.setRmCode(rmCode);
        rmTeam.setDeptHeadId(deptHeadId);
        rmTeam.setTeamLeaderId(teamLeaderId);
        rmTeam.setSrmId(srmId);
        rmTeam.setDeptCode(deptCode);
        rmTeam.setCreatedDate(new Date());
        rmTeam.setLastUpdatedDate(new Date());
        rmTeams.put(userId, rmTeam);
    }

    private void setupContracts() {
        contracts = new HashMap<>();

        // RM_1 manages STORE1 and STORE2 (under SRM_1)
        createContract("CNT001", RM_1_ID, stores.get("STORE1"), "ACTIVE", null);
        createContract("CNT002", RM_1_ID, stores.get("STORE2"), "ACTIVE", null);

        // RM_2 manages STORE3 (under SRM_1)
        createContract("CNT003", RM_2_ID, stores.get("STORE3"), "ACTIVE", null);

        // RM_3 manages STORE4 and STORE5 (under SRM_2)
        createContract("CNT004", RM_3_ID, stores.get("STORE4"), "ACTIVE", null);
        createContract("CNT005", RM_3_ID, stores.get("STORE5"), "INACTIVE", null);

        // RM_4 manages STORE6 and STORE7 (not under any SRM)
        createContract("CNT006", RM_4_ID, stores.get("STORE6"), "ACTIVE", null);
        createContract("CNT007", RM_4_ID, stores.get("STORE7"), "ACTIVE", 1);

        // RM_5 manages STORE8 (not under any SRM)
        createContract("CNT008", RM_5_ID, stores.get("STORE8"), "ACTIVE", null);

        // RM_6 manages STORE9 (under SRM_2)
        createContract("CNT009", RM_6_ID, stores.get("STORE9"), "ACTIVE", null);
    }

    private void createContract(String contractNo, int rmId, StoreEntity store, String status, Integer masterContractId) {
        ContractEntity contract = new ContractEntity();
        contract.setContractNo(contractNo);
        contract.setRmId(rmId);
        contract.setStore(store);
        contract.setStoreId(store.getId());
        contract.setMerchantId(1);
        contract.setStatus(status);
        contract.setStartDate(new Date());
        contract.setEndDate(new Date());
        contract.setCreatedDate(new Date());
        contract.setLastUpdatedDate(new Date());
		contract.setMasterContractId(masterContractId);
        contracts.put(contractNo, contract);
    }

    private void saveTestData() {
        rmTeamRepository.saveAll(rmTeams.values());
        contractRepository.saveAll(contracts.values());
        entityManager.flush();
        entityManager.clear();
    }

    @Test
    void whenRoleIsRM_thenReturnOnlyOwnStores() {
        // When
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(RM_1_ID, RoleCodeEnum.RM);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT001", "CNT002");
    }

    @Test
    void whenRoleIsRML_thenReturnTeamStores() {
        // When - RML_1 should see stores managed by RM_1, RM_2, and RM_5 (all under RML_1)
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(RML_1_ID, RoleCodeEnum.RML);

        // Then
        assertThat(result).hasSize(4);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT001", "CNT002", "CNT003", "CNT008");
    }

    @Test
    void whenRoleIsRMO_thenReturnDepartmentStores() {
        // When - RMO_2 should see all stores in DEPT_2 (managed by RM_3, RM_4, and RM_6, only master contracts)
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(RMO_2_ID, RoleCodeEnum.RMO);

        // Then - CNT007 is excluded because it's not a master contract
        assertThat(result).hasSize(4);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT004", "CNT005", "CNT006", "CNT009");
    }

    @Test
    void whenRoleIsDeptHead_thenReturnAllDepartmentStores() {
        // When - Department Head should see all stores (excluding non-master contracts)
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(DEPT_HEAD_ID, RoleCodeEnum.DEPT_HEAD);

        // Then
        assertThat(result).hasSize(8);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT001", "CNT002", "CNT003", "CNT004", "CNT005", "CNT006", "CNT008", "CNT009");
    }

    @Test
    void whenRoleIsMerchantAdmin_thenReturnEmptyList() {
        // When
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(RM_1_ID, RoleCodeEnum.MERCHANT_ADMIN);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void whenRoleIsSRM_thenReturnSrmManagedStores() {
        // When - SRM_1 should see stores managed by RMs under their supervision (RM_1 and RM_2)
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(SRM_1_ID, RoleCodeEnum.SRM);

        // Then
        assertThat(result).hasSize(3);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT001", "CNT002", "CNT003");
    }

    @Test
    void whenRoleIsSRM_thenReturnOnlyDirectlyManagedRMStores() {
        // When - SRM_2 should see stores managed by RMs under their direct supervision (RM_3 and RM_6)
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(SRM_2_ID, RoleCodeEnum.SRM);

        // Then
        assertThat(result).hasSize(3);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT004", "CNT005", "CNT009");
    }

    @Test
    void whenRoleIsSRM_andNoDirectRMs_thenReturnEmptyList() {
        // Given - Create an SRM with no RMs under supervision
        int srmWithNoRMs = 3503;
        createRmTeam(srmWithNoRMs, "SRM003", DEPT_HEAD_ID, RML_1_ID, DEPT_1);
        rmTeamRepository.save(rmTeams.get(srmWithNoRMs));
        entityManager.flush();
        entityManager.clear();

        // When
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(srmWithNoRMs, RoleCodeEnum.SRM);

        // Then - Should return empty list since SRM has no RMs under supervision and no own contracts
        assertThat(result).isEmpty();
    }

    @Test
    void whenRoleIsRM_andUnderSRM_thenReturnOnlyOwnStores() {
        // When - RM under SRM supervision should still only see their own stores
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(RM_1_ID, RoleCodeEnum.RM);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT001", "CNT002");
    }

    @Test
    void whenRoleIsRM_andNotUnderSRM_thenReturnOnlyOwnStores() {
        // When - RM not under SRM supervision should still only see their own stores (only master contracts)
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(RM_4_ID, RoleCodeEnum.RM);

        // Then - Only CNT006 is returned because CNT007 is not a master contract
        assertThat(result).hasSize(1);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT006");
    }

    @Test
    void whenRoleIsRML_andHasSRMsUnderSupervision_thenReturnAllTeamStores() {
        // When - RML_2 should see all stores in their team, including those managed by SRMs
        List<AccessibleStoreInfoResponseDto> result = accessibleStoreInfoService.getAccessibleStores(RML_2_ID, RoleCodeEnum.RML);

        // Then - Should see stores from RM_3, RM_4, and RM_6 (including those under SRM_2)
        assertThat(result).hasSize(4);
        assertThat(result).extracting("contractNo")
                .containsExactlyInAnyOrder("CNT004", "CNT005", "CNT006", "CNT009");
    }
}
